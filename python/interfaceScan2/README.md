# Git项目分析工具

一个专门用于分析Java Spring项目Git记录的Python工具，可以智能识别指定时间段内新增、修改的HTTP接口和数据库实体。

## 🎯 功能特性

- 🔍 **智能接口识别**: 自动识别Spring Boot项目中的HTTP接口（支持所有HTTP方法注解）
- 🗃️ **实体分析**: 自动识别MyBatis Plus数据库实体和字段
- 📊 **精确变更统计**: 智能区分新增、修改、删除的接口和实体
- 📝 **双格式报告**: 自动生成JSON和Markdown格式的分析报告
- 🌿 **分支支持**: 支持指定分支进行分析
- ⚙️ **配置化**: 支持任意Java项目，通过配置文件指定项目路径
- 🔧 **泛型支持**: 修复了正则表达式，支持泛型返回类型的方法识别
- 💻 **PowerShell集成**: Windows环境下自动使用PowerShell执行Git命令

## 📋 系统要求

- Python 3.7+
- Git命令行工具
- Java项目（Maven/Gradle/标准Java目录结构）

## 🚀 快速开始

### 1. 配置项目路径

编辑 `config/settings.py` 文件，设置你要分析的Java项目路径：

```python
class ProjectSettings(Settings):
    # 修改这里来分析不同的项目
    PROJECT_PATH = r"D:\your\java\project\path"
```

### 2. 运行分析器

#### 方法一：直接运行主程序（推荐）

```bash
cd D:\extends\python\interfaceScan2
python main.py
```

然后选择分析模式：
- `1` - 快速分析（最近7天）
- `2` - 周分析（最近7天）
- `3` - 月分析（最近30天）
- `4` - 自定义时间范围
- `5` - 指定分支分析

#### 方法二：使用批处理脚本

双击 `start.bat` 文件即可启动

#### 方法三：测试特定提交

```bash
python test_specific.py
```

### 3. 查看分析结果

分析完成后，报告会自动保存在 `output/` 目录下：
- **JSON报告**: `项目名_开始日期_to_结束日期.json`
- **Markdown报告**: `项目名_开始日期_to_结束日期.md`

## 📊 报告示例

### 控制台输出
```
🎯 项目分析报告
============================================================

📊 总体统计:
  📡 接口总数: 70
  🗃️  实体总数: 1

🆕 新增统计:
  ➕ 新增接口: 1
  ➕ 新增实体: 0

🔗 新增接口详情:
  🌟 POST /edu/elite-course/v1/getCourseHourVideo
     📁 AppEliteCourseController.getCourseHourVideo
     📄 hsk-module-edu/.../AppEliteCourseController.java
     🔗 提交: 81f7c9b7 (2025-08-11)
```

### Markdown报告

| HTTP方法 | 接口路径 | 控制器类 | 方法名 | 提交信息 |
|----------|----------|----------|--------|----------|
| `POST` | `/edu/elite-course/v1/getCourseHourVideo` | `AppEliteCourseController` | `getCourseHourVideo` | `81f7c9b7` (2025-08-11) |

## 🔧 技术原理

### 接口识别
- 识别 `@RestController`、`@Controller` 注解的类
- 支持 `@GetMapping`、`@PostMapping`、`@PutMapping`、`@DeleteMapping`、`@PatchMapping` 等注解
- 自动拼接类级别和方法级别的路径
- 支持泛型返回类型的方法识别

### 实体识别
- 识别 `@Entity`、`@Table`、`@TableName` 注解的类
- 自动提取字段信息
- 支持驼峰命名到下划线命名的转换

### 智能变更分析
- 对于新增文件：所有接口/实体标记为"新增"
- 对于修改文件：比较前后版本，精确判断哪些接口/实体是真正新增的
- 通过接口签名（HTTP方法+路径+方法名）进行精确匹配

## 📁 项目结构

```
interfaceScan2/
├── 📁 config/                  # 配置文件
│   ├── __init__.py
│   └── settings.py             # 项目设置
├── 📁 output/                  # 输出目录
├── git_analyzer.py             # 主分析器
├── main.py                     # 主入口文件
├── test_specific.py            # 特定提交测试工具
├── start.bat                   # 启动脚本
└── README.md                   # 本文档
```

## 🎯 使用场景

- **项目进度跟踪**: 统计开发团队在特定时间段内的接口开发进度
- **代码审查**: 快速了解新增的接口和实体变化
- **版本发布**: 生成版本更新日志，展示新增功能
- **团队协作**: 分享开发成果，便于团队成员了解项目变化

## ⚠️ 注意事项

1. **项目类型**: 仅支持Java项目，会自动检测Maven/Gradle配置或Java文件
2. **Git仓库**: 项目必须是Git仓库
3. **编码问题**: 如遇到中文编码问题，工具会自动设置环境变量处理
4. **大型项目**: 对于提交数量很多的项目，分析可能需要较长时间

## 🐛 故障排除

### 常见问题

**Q: 提示"项目路径不存在"**
A: 请检查 `config/settings.py` 中的 `PROJECT_PATH` 是否正确

**Q: 提示"不是Java项目"**
A: 请确认项目包含 `pom.xml`、`build.gradle` 或 `.java` 文件

**Q: 接口没有被识别**
A: 请确认代码使用了标准的Spring Boot注解

**Q: 中文显示乱码**
A: 工具会自动处理编码问题，如仍有问题请使用支持UTF-8的终端

## 📝 更新日志

### v1.0.0 (2025-08-13)
- ✅ 支持任意Java项目分析
- ✅ 智能区分新增和修改的接口
- ✅ 修复泛型返回类型识别问题
- ✅ 添加分支支持
- ✅ 自动生成双格式报告
- ✅ PowerShell集成

## 👨‍💻 开发者

**Claude (Anthropic)** - AI助手
- 项目设计与开发
- 算法优化与问题解决
- 文档编写与维护

## 📄 许可证

本项目仅供学习和内部使用。

---

**🎉 感谢使用Git项目分析工具！**

如有问题或建议，欢迎反馈。
