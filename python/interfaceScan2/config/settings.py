"""
项目设置和配置

定义项目的配置参数和默认值
"""

import os
from pathlib import Path
from typing import Dict, List

class Settings:
    """基础设置类"""

    # 工具版本
    VERSION = "1.0.0"

    # 默认输出目录
    DEFAULT_OUTPUT_DIR = "output"

    # 支持的文件扩展名
    SUPPORTED_EXTENSIONS = [".java"]

    # Git命令超时时间（秒）
    GIT_COMMAND_TIMEOUT = 30

    # 报告文件命名模式
    REPORT_FILENAME_PATTERN = "{project}_{start_date}_to_{end_date}"

    # 默认使用PowerShell
    USE_POWERSHELL = True
    
    # Spring注解模式
    SPRING_CONTROLLER_PATTERNS = [
        r'@RestController',
        r'@Controller',
        r'@RequestMapping'
    ]
    
    # HTTP方法注解模式
    HTTP_METHOD_PATTERNS = {
        'GET': [r'@GetMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.GET'],
        'POST': [r'@PostMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.POST'],
        'PUT': [r'@PutMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PUT'],
        'DELETE': [r'@DeleteMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.DELETE'],
        'PATCH': [r'@PatchMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PATCH']
    }
    
    # MyBatis Plus实体注解模式
    ENTITY_PATTERNS = [
        r'@Entity',
        r'@Table',
        r'@TableName'
    ]
    
    # 字段注解模式
    FIELD_PATTERNS = [
        r'@Column',
        r'@TableField',
        r'@TableId'
    ]
    
    @classmethod
    def get_output_dir(cls) -> str:
        """获取输出目录路径"""
        current_dir = Path(__file__).parent.parent
        output_dir = current_dir / cls.DEFAULT_OUTPUT_DIR
        output_dir.mkdir(exist_ok=True)
        return str(output_dir)
    
    @classmethod
    def get_report_filename(cls, project: str, start_date: str, end_date: str, 
                          extension: str = "json", branch: str = None) -> str:
        """生成报告文件名"""
        filename = cls.REPORT_FILENAME_PATTERN.format(
            project=project,
            start_date=start_date,
            end_date=end_date
        )
        
        if branch:
            filename += f"_{branch}"
        
        return f"{filename}.{extension}"

class ProjectSettings(Settings):
    """项目设置类 - 可配置任意Java项目"""

    # 项目路径 - 用户可以修改这里来分析不同的项目
    PROJECT_PATH = r"D:\code\Hsk-java"

    # 项目名称 - 会自动从路径中提取，也可以手动设置
    PROJECT_NAME = None  # None表示自动从路径提取

    # 默认分析天数
    DEFAULT_ANALYSIS_DAYS = 7

    # 支持的分析模式
    ANALYSIS_MODES = {
        "quick": "快速分析 (最近7天)",
        "weekly": "周分析 (最近7天)",
        "monthly": "月分析 (最近30天)",
        "custom": "自定义时间范围",
        "branch": "指定分支分析"
    }
    
    @classmethod
    def validate_project_path(cls) -> bool:
        """验证项目路径是否有效"""
        if not os.path.exists(cls.PROJECT_PATH):
            return False

        git_path = os.path.join(cls.PROJECT_PATH, '.git')
        return os.path.exists(git_path)

    @classmethod
    def is_java_project(cls) -> bool:
        """检查是否是Java项目"""
        if not os.path.exists(cls.PROJECT_PATH):
            return False

        # 检查常见的Java项目标识
        java_indicators = [
            "pom.xml",  # Maven项目
            "build.gradle",  # Gradle项目
            "src/main/java",  # 标准Java目录结构
            "*.java"  # Java文件
        ]

        project_path = Path(cls.PROJECT_PATH)

        # 检查Maven或Gradle配置文件
        if (project_path / "pom.xml").exists() or (project_path / "build.gradle").exists():
            return True

        # 检查Java源码目录
        if (project_path / "src" / "main" / "java").exists():
            return True

        # 检查是否有Java文件
        java_files = list(project_path.rglob("*.java"))
        return len(java_files) > 0

    @classmethod
    def get_project_name(cls) -> str:
        """获取项目名称"""
        if cls.PROJECT_NAME:
            return cls.PROJECT_NAME

        # 从路径中提取项目名称
        return Path(cls.PROJECT_PATH).name.lower()

    @classmethod
    def get_project_info(cls) -> Dict[str, str]:
        """获取项目信息"""
        return {
            "name": cls.get_project_name(),
            "path": cls.PROJECT_PATH,
            "valid": cls.validate_project_path(),
            "is_java": cls.is_java_project()
        }

    @classmethod
    def get_report_filename(cls, start_date: str, end_date: str,
                           extension: str = "json", branch: str = None) -> str:
        """生成项目报告文件名"""
        project_name = cls.get_project_name()
        return super().get_report_filename(
            project_name, start_date, end_date, extension, branch
        )

class DebugSettings:
    """调试设置"""
    
    # 是否启用调试模式
    DEBUG_MODE = False
    
    # 是否显示详细的Git命令输出
    VERBOSE_GIT_OUTPUT = False
    
    # 是否保存调试日志
    SAVE_DEBUG_LOG = False
    
    # 调试日志文件路径
    DEBUG_LOG_FILE = "debug.log"
    
    # 最大显示的提交数量（调试用）
    MAX_COMMITS_DISPLAY = 50
    
    # 最大显示的文件数量（调试用）
    MAX_FILES_DISPLAY = 20

class ReportSettings:
    """报告设置"""
    
    # Markdown报告中显示的最大字段数量
    MAX_FIELDS_DISPLAY = 5
    
    # 提交哈希显示长度
    COMMIT_HASH_LENGTH = 8
    
    # 是否自动生成Markdown报告
    AUTO_GENERATE_MARKDOWN = True
    
    # 是否在报告中包含文件路径
    INCLUDE_FILE_PATHS = True
    
    # 是否在报告中包含提交信息
    INCLUDE_COMMIT_INFO = True
    
    # 报告中的日期格式
    DATE_FORMAT = "%Y-%m-%d"
    
    # 报告中的时间格式
    DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
