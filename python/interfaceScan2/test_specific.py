#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定提交的接口识别
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from git_analyzer import GitInterfaceAnalyzer
from config.settings import ProjectSettings

def test_specific_commit():
    """测试特定提交"""
    target_commit = "81f7c9b7"
    
    print("🧪 测试特定提交的接口识别")
    print("="*60)
    print(f"目标提交: {target_commit}")
    
    # 创建分析器
    analyzer = GitInterfaceAnalyzer(ProjectSettings.PROJECT_PATH)
    
    # 获取提交的变更文件
    print(f"\n📁 获取提交的变更文件...")
    changed_files = analyzer.get_changed_files(target_commit)
    
    print(f"找到 {len(changed_files)} 个Java文件变更:")
    for status, file_path in changed_files:
        print(f"  {status} {file_path}")
    
    # 分析AppEliteCourseController.java文件
    target_file = "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java"
    
    print(f"\n🔍 分析目标文件: {target_file}")
    
    # 获取当前版本内容
    current_content = analyzer.get_file_content_at_commit(target_commit, target_file)
    if not current_content:
        print("❌ 无法获取当前版本内容")
        return
    
    print(f"✅ 获取当前版本内容成功 ({len(current_content)} 字符)")
    
    # 获取前一版本内容
    previous_content = analyzer.get_file_content_before_commit(target_commit, target_file)
    if not previous_content:
        print("⚠️  无法获取前一版本内容（可能是新文件）")
        previous_content = ""
    else:
        print(f"✅ 获取前一版本内容成功 ({len(previous_content)} 字符)")
    
    # 分析接口变化
    print(f"\n🔍 分析接口变化...")
    
    try:
        interface_changes = analyzer.analyze_interface_changes(
            previous_content, current_content, target_file, target_commit, "2025-08-11"
        )
        
        print(f"📊 找到 {len(interface_changes)} 个接口:")
        
        for i, interface in enumerate(interface_changes, 1):
            operation_emoji = {"ADD": "➕", "MODIFY": "✏️", "DELETE": "❌"}.get(interface.operation, "🔄")
            print(f"  {i}. {operation_emoji} {interface.method} {interface.path}")
            print(f"     方法: {interface.method_name}")
            print(f"     操作: {interface.operation}")
            
            # 检查是否是目标接口
            if 'getCourseHourVideo' in interface.method_name:
                print(f"     🎯 这是目标接口!")
            print()
        
        # 统计结果
        add_count = len([i for i in interface_changes if i.operation == "ADD"])
        modify_count = len([i for i in interface_changes if i.operation == "MODIFY"])
        
        print(f"📈 统计结果:")
        print(f"  ➕ 新增接口: {add_count}")
        print(f"  ✏️ 修改接口: {modify_count}")
        
        # 检查目标接口
        target_interfaces = [i for i in interface_changes if 'getCourseHourVideo' in i.method_name]
        if target_interfaces:
            target_interface = target_interfaces[0]
            print(f"\n🎯 目标接口分析:")
            print(f"  接口: {target_interface.method} {target_interface.path}")
            print(f"  方法: {target_interface.method_name}")
            print(f"  操作: {target_interface.operation}")
            
            if target_interface.operation == "ADD":
                print(f"  ✅ 正确识别为新增接口!")
            else:
                print(f"  ❌ 错误识别为: {target_interface.operation}")
        else:
            print(f"\n❌ 未找到目标接口 getCourseHourVideo")
        
    except Exception as e:
        print(f"❌ 分析接口变化时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    test_specific_commit()

if __name__ == "__main__":
    main()
