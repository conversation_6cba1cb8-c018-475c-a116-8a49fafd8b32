#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Git接口和实体分析工具 - 主分析器

基于原有功能重新整理，支持任意Java项目分析
"""

import os
import re
import json
import subprocess
from datetime import datetime, timedelta
from typing import List, Dict, Set, Tu<PERSON>
from dataclasses import dataclass, asdict
from pathlib import Path

# 导入配置
from config.settings import ProjectSettings

@dataclass
class HttpInterface:
    """HTTP接口信息"""
    method: str          # GET, POST, PUT, DELETE等
    path: str           # 接口路径
    class_name: str     # 所在类名
    method_name: str    # 方法名
    file_path: str      # 文件路径
    commit_hash: str    # 提交哈希
    commit_date: str    # 提交日期
    operation: str      # ADD, MODIFY, DELETE

@dataclass
class DatabaseEntity:
    """数据库实体信息"""
    table_name: str     # 表名
    class_name: str     # 实体类名
    file_path: str      # 文件路径
    fields: List[str]   # 字段列表
    commit_hash: str    # 提交哈希
    commit_date: str    # 提交日期
    operation: str      # ADD, MODIFY, DELETE

class GitInterfaceAnalyzer:
    """Git接口分析器"""
    
    def __init__(self, repo_path: str):
        self.repo_path = Path(repo_path)
        
        # Spring Controller注解模式
        self.controller_patterns = [
            r'@RestController',
            r'@Controller',
            r'@RequestMapping'
        ]
        
        # HTTP方法注解模式
        self.http_method_patterns = {
            'GET': [r'@GetMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.GET'],
            'POST': [r'@PostMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.POST'],
            'PUT': [r'@PutMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PUT'],
            'DELETE': [r'@DeleteMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.DELETE'],
            'PATCH': [r'@PatchMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PATCH']
        }
        
        # MyBatis Plus实体注解模式
        self.entity_patterns = [
            r'@Entity',
            r'@Table',
            r'@TableName'
        ]
        
        # 字段注解模式
        self.field_patterns = [
            r'@Column',
            r'@TableField',
            r'@TableId'
        ]
    
    def run_git_command(self, command: List[str]) -> str:
        """执行Git命令"""
        try:
            # 设置Git输出为英文，避免中文编码问题
            env = os.environ.copy()
            env['LC_ALL'] = 'C'
            env['LANG'] = 'C'

            result = subprocess.run(
                command,
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                timeout=30,
                env=env
            )

            if result.returncode != 0:
                # 忽略一些常见的非致命错误
                if result.stderr and "does not exist" not in result.stderr:
                    print(f"Git命令警告: {' '.join(command)}")
                return ""
            return result.stdout
        except Exception as e:
            print(f"执行Git命令时出错: {e}")
            return ""
    
    def get_commits_in_range(self, start_date: str, end_date: str, branch: str = None) -> List[Tuple[str, str]]:
        """获取指定时间范围内的提交"""
        command = [
            'git', 'log',
            f'--since={start_date}',
            f'--until={end_date}',
            '--pretty=format:%H|%ci',
            '--reverse'
        ]
        
        # 如果指定了分支，添加分支参数
        if branch:
            command.append(branch)
        
        output = self.run_git_command(command)
        commits = []
        
        for line in output.strip().split('\n'):
            if line and '|' in line:
                commit_hash, commit_date = line.split('|', 1)
                commits.append((commit_hash.strip(), commit_date.strip()))
        
        return commits
    
    def get_changed_files(self, commit_hash: str) -> List[Tuple[str, str]]:
        """获取指定提交中变更的Java文件"""
        command = ['git', 'show', '--name-status', '--format=', commit_hash]
        output = self.run_git_command(command)
        
        changed_files = []
        for line in output.strip().split('\n'):
            if line and '\t' in line:
                parts = line.split('\t')
                if len(parts) >= 2:
                    status = parts[0]
                    file_path = parts[1]
                    if file_path.endswith('.java'):
                        changed_files.append((status, file_path))
        
        return changed_files
    
    def get_file_content_at_commit(self, commit_hash: str, file_path: str) -> str:
        """获取指定提交时的文件内容"""
        command = ['git', 'show', f'{commit_hash}:{file_path}']
        return self.run_git_command(command)

    def get_file_content_before_commit(self, commit_hash: str, file_path: str) -> str:
        """获取指定提交前一个版本的文件内容"""
        command = ['git', 'show', f'{commit_hash}~1:{file_path}']
        return self.run_git_command(command)
    
    def extract_interfaces_from_content(self, content: str, file_path: str, 
                                      commit_hash: str, commit_date: str, 
                                      operation: str) -> List[HttpInterface]:
        """从文件内容中提取HTTP接口"""
        interfaces = []
        
        # 检查是否是Controller类
        is_controller = False
        for pattern in self.controller_patterns:
            if re.search(pattern, content):
                is_controller = True
                break
        
        if not is_controller:
            return interfaces
        
        lines = content.split('\n')
        
        # 提取类名和基础路径
        class_name = ""
        class_base_path = ""
        
        for line in lines:
            # 提取类名
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                class_name = class_match.group(1)
            
            # 提取类级别的RequestMapping路径
            if '@RequestMapping' in line:
                path_match = re.search(r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']', line)
                if path_match:
                    class_base_path = path_match.group(1)
        
        if not class_name:
            return interfaces
        
        # 查找HTTP方法注解
        for i, line in enumerate(lines):
            for method, patterns in self.http_method_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line):
                        # 提取路径
                        path = ""
                        path_match = re.search(r'["\']([^"\']+)["\']', line)
                        if path_match:
                            path = path_match.group(1)
                        
                        # 查找下一个方法定义 - 修复正则表达式支持泛型
                        method_name = ""
                        for j in range(i + 1, min(i + 10, len(lines))):
                            method_match = re.search(r'public\s+[^(]+\s+(\w+)\s*\(', lines[j])
                            if method_match:
                                method_name = method_match.group(1)
                                break
                        
                        # 构建完整路径
                        full_path = class_base_path + path if class_base_path else path
                        
                        if method_name:
                            interface = HttpInterface(
                                method=method,
                                path=full_path,
                                class_name=class_name,
                                method_name=method_name,
                                file_path=file_path,
                                commit_hash=commit_hash,
                                commit_date=commit_date,
                                operation=operation
                            )
                            interfaces.append(interface)
                        break
        
        return interfaces
    
    def extract_entities_from_content(self, content: str, file_path: str,
                                    commit_hash: str, commit_date: str,
                                    operation: str) -> List[DatabaseEntity]:
        """从文件内容中提取数据库实体"""
        entities = []
        
        # 检查是否是实体类
        is_entity = False
        for pattern in self.entity_patterns:
            if re.search(pattern, content):
                is_entity = True
                break
        
        if not is_entity:
            return entities
        
        lines = content.split('\n')
        
        # 提取类名和表名
        class_name = ""
        table_name = ""
        
        for line in lines:
            # 提取类名
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                class_name = class_match.group(1)
                if not table_name:
                    # 默认表名为类名转下划线
                    table_name = self.camel_to_snake(class_name)
            
            # 提取表名
            if '@TableName' in line or '@Table' in line:
                name_match = re.search(r'["\']([^"\']+)["\']', line)
                if name_match:
                    table_name = name_match.group(1)
        
        # 提取字段
        fields = []
        for i, line in enumerate(lines):
            # 检查字段注解
            has_field_annotation = False
            for pattern in self.field_patterns:
                if re.search(pattern, line):
                    has_field_annotation = True
                    break
            
            if has_field_annotation:
                # 查找下一行的字段定义
                for j in range(i + 1, min(i + 5, len(lines))):
                    field_match = re.search(r'private\s+\w+\s+(\w+)\s*;', lines[j])
                    if field_match:
                        field_name = field_match.group(1)
                        if field_name not in fields:
                            fields.append(field_name)
                        break
            else:
                # 直接匹配字段定义（没有注解的情况）
                field_match = re.search(r'private\s+\w+\s+(\w+)\s*;', line)
                if field_match:
                    field_name = field_match.group(1)
                    if field_name not in fields:
                        fields.append(field_name)
        
        if class_name:
            entity = DatabaseEntity(
                table_name=table_name,
                class_name=class_name,
                file_path=file_path,
                fields=fields,
                commit_hash=commit_hash,
                commit_date=commit_date,
                operation=operation
            )
            entities.append(entity)
        
        return entities
    
    def camel_to_snake(self, name: str) -> str:
        """驼峰命名转下划线命名"""
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    def analyze_interface_changes(self, previous_content: str, current_content: str,
                                 file_path: str, commit_hash: str, commit_date: str) -> List[HttpInterface]:
        """分析接口变化，判断哪些接口是新增的"""
        interfaces = []

        # 提取前一版本的接口
        previous_interfaces = self.extract_interfaces_from_content(
            previous_content, file_path, commit_hash, commit_date, "TEMP"
        ) if previous_content else []

        # 提取当前版本的接口
        current_interfaces = self.extract_interfaces_from_content(
            current_content, file_path, commit_hash, commit_date, "TEMP"
        )

        # 创建前一版本接口的标识集合（用于快速查找）
        previous_interface_signatures = set()
        for interface in previous_interfaces:
            signature = f"{interface.method}:{interface.path}:{interface.method_name}"
            previous_interface_signatures.add(signature)

        # 分析当前版本的接口
        for interface in current_interfaces:
            signature = f"{interface.method}:{interface.path}:{interface.method_name}"

            if signature in previous_interface_signatures:
                # 接口已存在，标记为修改
                interface.operation = "MODIFY"
            else:
                # 接口不存在于前一版本，标记为新增
                interface.operation = "ADD"

            interfaces.append(interface)

        return interfaces

    def analyze_entity_changes(self, previous_content: str, current_content: str,
                              file_path: str, commit_hash: str, commit_date: str) -> List[DatabaseEntity]:
        """分析实体变化，判断哪些实体是新增的"""
        entities = []

        # 提取前一版本的实体
        previous_entities = self.extract_entities_from_content(
            previous_content, file_path, commit_hash, commit_date, "TEMP"
        ) if previous_content else []

        # 提取当前版本的实体
        current_entities = self.extract_entities_from_content(
            current_content, file_path, commit_hash, commit_date, "TEMP"
        )

        # 创建前一版本实体的标识集合
        previous_entity_signatures = set()
        for entity in previous_entities:
            signature = f"{entity.table_name}:{entity.class_name}"
            previous_entity_signatures.add(signature)

        # 分析当前版本的实体
        for entity in current_entities:
            signature = f"{entity.table_name}:{entity.class_name}"

            if signature in previous_entity_signatures:
                # 实体已存在，标记为修改
                entity.operation = "MODIFY"
            else:
                # 实体不存在于前一版本，标记为新增
                entity.operation = "ADD"

            entities.append(entity)

        return entities
    
    def analyze_period(self, start_date: str, end_date: str, branch: str = None) -> Dict:
        """分析指定时间段的变化"""
        print(f"分析时间段: {start_date} 到 {end_date}")
        if branch:
            print(f"分析分支: {branch}")
        
        # 获取时间范围内的提交
        commits = self.get_commits_in_range(start_date, end_date, branch)
        print(f"找到 {len(commits)} 个提交")
        
        interfaces = []
        entities = []
        
        for commit_hash, commit_date in commits:
            print(f"分析提交: {commit_hash[:8]} ({commit_date})")
            
            # 获取变更的文件
            changed_files = self.get_changed_files(commit_hash)
            
            for status, file_path in changed_files:
                # 获取文件内容
                if status != "D":  # 不是删除的文件
                    current_content = self.get_file_content_at_commit(commit_hash, file_path)
                    if current_content:
                        # 对于修改的文件，需要比较前后差异来判断接口是否新增
                        if status == "M":  # 修改的文件
                            # 获取前一个版本的文件内容
                            previous_content = self.get_file_content_before_commit(commit_hash, file_path)

                            # 分析接口和实体的变化
                            interface_changes = self.analyze_interface_changes(
                                previous_content, current_content, file_path, commit_hash, commit_date
                            )
                            interfaces.extend(interface_changes)

                            entity_changes = self.analyze_entity_changes(
                                previous_content, current_content, file_path, commit_hash, commit_date
                            )
                            entities.extend(entity_changes)

                        else:  # 新增的文件
                            operation = "ADD" if status == "A" else "MODIFY"

                            # 提取接口
                            file_interfaces = self.extract_interfaces_from_content(
                                current_content, file_path, commit_hash, commit_date, operation
                            )
                            interfaces.extend(file_interfaces)

                            # 提取实体
                            file_entities = self.extract_entities_from_content(
                                current_content, file_path, commit_hash, commit_date, operation
                            )
                            entities.extend(file_entities)
        
        return self.generate_report(interfaces, entities, start_date, end_date, branch)
    
    def generate_report(self, interfaces: List[HttpInterface], entities: List[DatabaseEntity],
                       start_date: str, end_date: str, branch: str = None) -> Dict:
        """生成分析报告"""
        # 统计信息
        summary = {
            "total_interfaces": len(interfaces),
            "total_entities": len(entities),
            "new_interfaces": len([i for i in interfaces if i.operation == "ADD"]),
            "new_entities": len([e for e in entities if e.operation == "ADD"]),
            "modified_interfaces": len([i for i in interfaces if i.operation == "MODIFY"]),
            "modified_entities": len([e for e in entities if e.operation == "MODIFY"]),
            "deleted_interfaces": len([i for i in interfaces if i.operation == "DELETE"]),
            "deleted_entities": len([e for e in entities if e.operation == "DELETE"])
        }
        
        return {
            "summary": summary,
            "interfaces": [asdict(interface) for interface in interfaces],
            "entities": [asdict(entity) for entity in entities],
            "analysis_info": {
                "start_date": start_date,
                "end_date": end_date,
                "branch": branch,
                "generated_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
    
    def save_report(self, report: Dict, output_file: str):
        """保存分析报告"""
        # 保存JSON报告
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"JSON报告已保存到: {output_file}")
        
        # 自动生成Markdown报告
        try:
            markdown_file = output_file.replace('.json', '.md')
            self.generate_markdown_report(report, markdown_file)
            print(f"Markdown报告已保存到: {markdown_file}")
        except Exception as e:
            print(f"生成Markdown报告时出错: {e}")
    
    def generate_markdown_report(self, report_data: Dict, output_file: str):
        """生成Markdown报告"""
        # 这里使用简化的Markdown生成逻辑
        summary = report_data.get('summary', {})
        interfaces = report_data.get('interfaces', [])
        entities = report_data.get('entities', [])
        
        markdown_content = []
        
        # 标题和基本信息
        markdown_content.append("# Git分析报告")
        markdown_content.append("")
        markdown_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_content.append("")
        
        # 总体统计
        markdown_content.append("## 📊 总体统计")
        markdown_content.append("")
        markdown_content.append("| 类型 | 总数 | 新增 | 修改 | 删除 |")
        markdown_content.append("|------|------|------|------|------|")
        markdown_content.append(f"| 🔗 HTTP接口 | {summary.get('total_interfaces', 0)} | {summary.get('new_interfaces', 0)} | {summary.get('modified_interfaces', 0)} | {summary.get('deleted_interfaces', 0)} |")
        markdown_content.append(f"| 🗃️ 数据库实体 | {summary.get('total_entities', 0)} | {summary.get('new_entities', 0)} | {summary.get('modified_entities', 0)} | {summary.get('deleted_entities', 0)} |")
        markdown_content.append("")
        
        # 新增接口详情
        new_interfaces = [i for i in interfaces if i.get('operation') == 'ADD']
        if new_interfaces:
            markdown_content.append("## 🆕 新增接口详情")
            markdown_content.append("")
            markdown_content.append("| HTTP方法 | 接口路径 | 控制器类 | 方法名 | 提交信息 |")
            markdown_content.append("|----------|----------|----------|--------|----------|")
            
            for interface in new_interfaces:
                method = interface.get('method', '')
                path = interface.get('path', '')
                class_name = interface.get('class_name', '')
                method_name = interface.get('method_name', '')
                commit_hash = interface.get('commit_hash', '')[:8]
                commit_date = interface.get('commit_date', '')[:10]
                
                markdown_content.append(f"| `{method}` | `{path}` | `{class_name}` | `{method_name}` | `{commit_hash}` ({commit_date}) |")
            
            markdown_content.append("")
        
        # 新增实体详情
        new_entities = [e for e in entities if e.get('operation') == 'ADD']
        if new_entities:
            markdown_content.append("## 🆕 新增数据库实体详情")
            markdown_content.append("")
            markdown_content.append("| 表名 | 实体类名 | 字段列表 | 提交信息 |")
            markdown_content.append("|------|----------|----------|----------|")
            
            for entity in new_entities:
                table_name = entity.get('table_name', '')
                class_name = entity.get('class_name', '')
                fields = entity.get('fields', [])
                fields_str = ', '.join(fields[:5])  # 只显示前5个字段
                if len(fields) > 5:
                    fields_str += f" ... (共{len(fields)}个字段)"
                commit_hash = entity.get('commit_hash', '')[:8]
                commit_date = entity.get('commit_date', '')[:10]
                
                markdown_content.append(f"| `{table_name}` | `{class_name}` | {fields_str} | `{commit_hash}` ({commit_date}) |")
            
            markdown_content.append("")
        
        # 技术说明
        markdown_content.append("## 📋 技术说明")
        markdown_content.append("")
        markdown_content.append("- **HTTP接口**: 识别Spring Boot项目中的`@RestController`、`@Controller`注解的类")
        markdown_content.append("- **数据库实体**: 识别MyBatis Plus项目中的`@Entity`、`@Table`、`@TableName`注解的类")
        markdown_content.append("")
        markdown_content.append("---")
        markdown_content.append("")
        markdown_content.append("*本报告由Git分析工具自动生成*")
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
