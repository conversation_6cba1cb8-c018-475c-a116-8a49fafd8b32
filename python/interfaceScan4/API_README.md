# API接口注释完整性检查工具

这个工具用于检查Apifox接口文档中的参数是否都添加了注释，帮助确保API文档的完整性。

## 功能特性

- ✅ 自动从Apifox API列表中提取所有接口
- ✅ 检查请求参数、响应参数的注释完整性
- ✅ 支持嵌套对象和数组的递归检查
- ✅ 生成详细的检查报告
- ✅ 支持跳过特定类型的参数（如header参数）
- ✅ 输出JSON格式的详细结果和文本格式的汇总

## 文件说明

- `api_comment_checker.py` - 主要的检查器类
- `config.py` - 配置文件，包含Cookie和URL等配置
- `run_check.py` - 简单的运行脚本
- `API_README.md` - 使用说明

## 安装依赖

```bash
pip install requests pyyaml urllib3
```

## 使用方法

### 方法1：直接运行

```bash
python run_check.py
```

### 方法2：使用主模块

```bash
python api_comment_checker.py
```

### 方法3：在代码中使用

```python
from api_comment_checker import ApiCommentChecker

# 创建检查器
checker = ApiCommentChecker(cookie="your_apifox_cookie")

# 检查API列表
results = checker.check_api_list("your_api_list_url")

# 打印报告
checker.print_summary_report(results)
```

## 配置说明

在 `config.py` 中修改以下配置：

1. **APIFOX_COOKIE**: 你的Apifox Cookie，用于访问私有文档
2. **API_LIST_URL**: API列表的URL
3. **REQUEST_TIMEOUT**: 请求超时时间
4. **SKIP_SCHEMAS**: 需要跳过检查的Schema名称
5. **SKIP_PARAM_LOCATIONS**: 需要跳过检查的参数位置

## 获取Apifox Cookie

1. 打开浏览器，登录Apifox
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面或访问任意API文档
5. 在请求头中找到Cookie字段，复制其值
6. 将Cookie值粘贴到 `config.py` 中的 `APIFOX_COOKIE`

## 输出文件

运行后会生成以下文件：

- `api_comment_check_results.json` - 详细的检查结果（JSON格式）
- `missing_comments_summary.txt` - 缺失注释的汇总（文本格式）

## 报告示例

```
================================================================================
📊 API注释完整性检查报告
================================================================================
📋 总接口数: 45
✅ 已检查接口数: 45
⚠️  有缺失注释的接口数: 12
📝 缺失注释参数总数: 28

⚠️  注释完整率: 73.3%

📋 缺失注释详情:
--------------------------------------------------------------------------------

🔸 接口: 首页相关信息 - 首页相关信息
   URL: https://s.apifox.cn/96438ace-5a09-4ed0-bf36-d83b44739051/324207254e0.md
   缺失注释数: 3
   - 参数: hskLevel
     位置: 请求体(application/json)
     路径: /home/<USER>
```

## 常见问题

### Q: 为什么无法获取API内容？
A: 请检查Cookie是否正确，或者Cookie是否已过期。

### Q: 如何跳过某些不需要检查的参数？
A: 在 `config.py` 中的 `SKIP_SCHEMAS` 和 `SKIP_PARAM_LOCATIONS` 中添加需要跳过的项目。

### Q: 检查结果不准确怎么办？
A: 请确保API文档是标准的OpenAPI 3.0格式，工具目前只支持这种格式。

## 优化建议

1. **定期检查**: 建议在API文档更新后定期运行此工具
2. **CI/CD集成**: 可以将此工具集成到CI/CD流程中，确保新增API都有完整注释
3. **团队规范**: 建立团队规范，要求所有API参数都必须添加注释

## 更新日志

- v1.0.0: 初始版本，支持基本的注释完整性检查
- v1.1.0: 优化错误处理，增加详细报告
- v1.2.0: 支持嵌套对象和数组的递归检查
