#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Schema检查逻辑
"""

import yaml
from api_comment_checker import ApiCommentChecker

# 测试数据 - 模拟有缺失注释的Schema
test_yaml = """
openapi: 3.0.1
info:
  title: 'Test API'
  version: 1.0.0
paths:
  /test/api:
    post:
      summary: 测试接口
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestResponse'
components:
  schemas:
    TestResponse:
      type: object
      properties:
        id:
          type: integer
          description: ID
        progress:
          type: integer
          # 注意：这里故意没有description
        name:
          type: string
          description: 名称
"""

def test_schema_check():
    """测试Schema检查"""
    print("🧪 测试Schema检查逻辑...")
    
    # 解析测试数据
    openapi_data = yaml.safe_load(test_yaml)
    
    # 创建检查器
    checker = ApiCommentChecker()
    
    # 执行检查
    missing_comments = checker.validate_api_descriptions(openapi_data)
    
    print(f"检测到 {len(missing_comments)} 个缺失注释的参数:")
    for missing in missing_comments:
        print(f"  - 参数: {missing.get('参数名称', 'N/A')}")
        print(f"    位置: {missing.get('参数位置', 'N/A')}")
        print(f"    接口路径: {missing.get('接口路径', 'N/A')}")
        print(f"    接口说明: {missing.get('接口说明', 'N/A')}")
        print()

if __name__ == "__main__":
    test_schema_check()
