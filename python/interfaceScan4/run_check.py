#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行API注释检查的简单脚本
"""

from api_comment_checker import ApiCommentChecker
import json

# 直接在这里定义配置，避免导入冲突
API_LIST_URL = "https://s.apifox.cn/96438ace-5a09-4ed0-bf36-d83b44739051/llms.txt"
APIFOX_COOKIE = "abflag=1741329037; projectCid=8mPkcQMX-8qIC-Ac5j-Zqh4-T278Gf6gkdd9; AGL_USER_ID=c2b02a20-20d7-4fac-88ca-59f79ea1595a; _ga=GA1.1.*********.**********; Hm_lvt_34be24cd47c1c8849a4b4a87748e6979=**********; _uetvid=b10b4790fb1d11ef85d7d1f1282b89c8; _gcl_au=1.1.**********.**********; _ga_NTLDK3J296=GS2.1.s1750125012$o45$g1$t1750125015$j57$l0$h0; acw_tc=0a03834117528177810158739e5fd57bb418bd8e9a1b5d4326bb25a4c1897a; sharedDoc=VX-9JZV2XW1e8JDqtAqvhpqE-b0aLDDSu3_1Fu36-VPoOzKspuCpMS098Invs1Uo; Hm_lvt_56f20ef8b9cc36436162ef11afabac21=**********; Hm_lpvt_56f20ef8b9cc36436162ef11afabac21=**********; HMACCOUNT=19BC3957598376B9; isTracking=true"

def main():
    """主函数"""
    print("🚀 开始API注释完整性检查...")
    
    # 创建检查器
    checker = ApiCommentChecker(cookie=APIFOX_COOKIE)
    
    # 执行检查
    results = checker.check_api_list(API_LIST_URL)
    
    # 打印报告
    checker.print_summary_report(results)
    
    # 保存详细结果
    output_file = 'api_comment_check_results.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: {output_file}")
    
    # 如果有缺失注释，生成简化的缺失列表
    if results.get('all_missing_comments'):
        missing_file = 'missing_comments_summary.txt'
        with open(missing_file, 'w', encoding='utf-8') as f:
            f.write("API接口缺失注释汇总\n")
            f.write("=" * 50 + "\n\n")

            # 按接口分组并去重
            grouped_missing = {}
            for missing in results['all_missing_comments']:
                # 处理独立Schema定义的显示
                if missing.get('参数位置', '').startswith('Schema定义'):
                    api_key = f"独立Schema定义 ({missing.get('参数位置', 'N/A')})"
                    interface_path = missing.get('参数位置', 'N/A')
                else:
                    api_key = f"{missing.get('接口说明', 'N/A')} ({missing.get('接口路径', 'N/A')})"
                    interface_path = missing.get('接口路径', 'N/A')

                if api_key not in grouped_missing:
                    grouped_missing[api_key] = {
                        'interface_path': interface_path,
                        'params': set()  # 使用set去重
                    }

                # 添加参数信息（使用元组确保唯一性）
                param_info = (missing.get('参数名称', 'N/A'), missing.get('参数位置', 'N/A'))
                grouped_missing[api_key]['params'].add(param_info)

            # 输出分组后的结果
            for api_info, data in grouped_missing.items():
                f.write(f"\n接口: {api_info}\n")
                f.write("-" * 40 + "\n")

                for param_name, param_location in data['params']:
                    f.write(f"  - 参数: {param_name}\n")
                    f.write(f"    位置: {param_location}\n")
                    f.write(f"    接口路径: {data['interface_path']}\n")

        print(f"📝 缺失注释汇总已保存到: {missing_file}")

if __name__ == "__main__":
    main()
