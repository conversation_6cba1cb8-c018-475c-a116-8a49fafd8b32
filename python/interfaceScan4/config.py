#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# Apifox Cookie配置
# 请替换为你自己的cookie
APIFOX_COOKIE = "abflag=1741329037; projectCid=8mPkcQMX-8qIC-Ac5j-Zqh4-T278Gf6gkdd9; AGL_USER_ID=c2b02a20-20d7-4fac-88ca-59f79ea1595a; _ga=GA1.1.*********.**********; Hm_lvt_34be24cd47c1c8849a4b4a87748e6979=**********; _uetvid=b10b4790fb1d11ef85d7d1f1282b89c8; _gcl_au=1.1.**********.**********; _ga_NTLDK3J296=GS2.1.s1750125012$o45$g1$t1750125015$j57$l0$h0; acw_tc=0a03834117528177810158739e5fd57bb418bd8e9a1b5d4326bb25a4c1897a; sharedDoc=VX-9JZV2XW1e8JDqtAqvhpqE-b0aLDDSu3_1Fu36-VPoOzKspuCpMS098Invs1Uo; Hm_lvt_56f20ef8b9cc36436162ef11afabac21=**********; Hm_lpvt_56f20ef8b9cc36436162ef11afabac21=**********; HMACCOUNT=19BC3957598376B9; isTracking=true"

# API列表URL
API_LIST_URL = "https://s.apifox.cn/96438ace-5a09-4ed0-bf36-d83b44739051/llms.txt"

# 请求超时时间（秒）
REQUEST_TIMEOUT = 30

# 需要跳过检查的Schema名称（通用响应类型）
SKIP_SCHEMAS = ['AjaxResult', 'CommonResult', 'Result', 'PageResult']

# 需要跳过检查的参数位置
SKIP_PARAM_LOCATIONS = ['header']
