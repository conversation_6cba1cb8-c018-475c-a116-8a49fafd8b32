#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个API接口注释检查工具
用于快速检查单个API接口的注释完整性
"""

from interfaceScan4.api_comment_checker import ApiCommentChecker
import json

def check_single_api():
    """检查单个API接口"""
    print("🔍 单个API接口注释检查工具")
    print("=" * 50)
    
    # 获取用户输入
    api_url = input("请输入API文档URL: ").strip()
    if not api_url:
        print("❌ URL不能为空")
        return
    
    # 创建检查器
    checker = ApiCommentChecker()
    
    print(f"\n🔍 开始检查接口: {api_url}")
    
    # 获取接口内容
    api_content = checker.fetch_url_content(api_url)
    if not api_content:
        print("❌ 无法获取接口内容")
        return
    
    # 提取YAML内容
    yaml_content = checker.extract_yaml_from_markdown(api_content)
    if not yaml_content:
        print("❌ 未找到YAML内容")
        return
    
    try:
        import yaml
        # 解析YAML
        openapi_data = yaml.safe_load(yaml_content)
        
        # 检查注释
        missing_comments = checker.validate_api_descriptions(openapi_data)
        
        # 显示结果
        print("\n" + "="*60)
        print("📊 检查结果")
        print("="*60)
        
        if missing_comments:
            print(f"⚠️  发现 {len(missing_comments)} 个缺失注释的参数:")
            print("-" * 60)
            
            for missing in missing_comments:
                print(f"🔸 参数: {missing.get('参数名称', 'N/A')}")
                print(f"   位置: {missing.get('参数位置', 'N/A')}")
                if missing.get('接口路径'):
                    print(f"   路径: {missing.get('接口路径')}")
                if missing.get('接口说明'):
                    print(f"   说明: {missing.get('接口说明')}")
                print()
        else:
            print("🎉 所有参数都有注释，文档完整！")
        
        # 保存结果
        result_file = 'single_api_check_result.json'
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'api_url': api_url,
                'missing_comments_count': len(missing_comments),
                'missing_comments': missing_comments
            }, f, ensure_ascii=False, indent=2)
        
        print(f"💾 详细结果已保存到: {result_file}")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")

def check_from_yaml_file():
    """从本地YAML文件检查"""
    print("🔍 从本地YAML文件检查注释")
    print("=" * 50)
    
    yaml_file = input("请输入YAML文件路径: ").strip()
    if not yaml_file:
        print("❌ 文件路径不能为空")
        return
    
    try:
        import yaml
        with open(yaml_file, 'r', encoding='utf-8') as f:
            openapi_data = yaml.safe_load(f)
        
        checker = ApiCommentChecker()
        missing_comments = checker.validate_api_descriptions(openapi_data)
        
        print("\n" + "="*60)
        print("📊 检查结果")
        print("="*60)
        
        if missing_comments:
            print(f"⚠️  发现 {len(missing_comments)} 个缺失注释的参数:")
            print("-" * 60)
            
            for missing in missing_comments:
                print(f"🔸 参数: {missing.get('参数名称', 'N/A')}")
                print(f"   位置: {missing.get('参数位置', 'N/A')}")
                if missing.get('接口路径'):
                    print(f"   路径: {missing.get('接口路径')}")
                print()
        else:
            print("🎉 所有参数都有注释，文档完整！")
            
    except FileNotFoundError:
        print(f"❌ 文件不存在: {yaml_file}")
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")

def main():
    """主函数"""
    print("🚀 API注释检查工具")
    print("=" * 50)
    print("1. 检查在线API文档")
    print("2. 检查本地YAML文件")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == '1':
            check_single_api()
            break
        elif choice == '2':
            check_from_yaml_file()
            break
        elif choice == '3':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入 1-3")

if __name__ == "__main__":
    main()
