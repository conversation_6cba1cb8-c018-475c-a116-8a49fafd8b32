@echo off
chcp 65001 >nul
echo 🚀 API接口注释完整性检查工具
echo ================================
echo.
echo 1. 检查所有API接口
echo 2. 检查单个API接口
echo 3. 退出
echo.
set /p choice="请选择操作 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🔍 开始检查所有API接口...
    python run_check.py
) else if "%choice%"=="2" (
    echo.
    echo 🔍 单个API接口检查...
    python single_api_check.py
) else if "%choice%"=="3" (
    echo 👋 再见！
    exit /b 0
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo ✅ 检查完成！
pause
