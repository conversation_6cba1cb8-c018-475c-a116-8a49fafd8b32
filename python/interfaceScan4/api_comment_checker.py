#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口注释完整性检查工具
用于检查Apifox接口文档中参数是否都添加了注释
"""

import yaml
import re
import requests
from requests.exceptions import RequestException
import urllib3
from typing import List, Dict, Any, Optional
import json

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ApiCommentChecker:
    """API注释检查器"""
    
    def __init__(self, cookie: str = None):
        """
        初始化检查器
        :param cookie: Apifox的cookie，用于访问私有文档
        """
        self.headers = {
            "cookie": cookie or "abflag=1741329037; projectCid=8mPkcQMX-8qIC-Ac5j-Zqh4-T278Gf6gkdd9; AGL_USER_ID=c2b02a20-20d7-4fac-88ca-59f79ea1595a; _ga=GA1.1.*********.**********; Hm_lvt_34be24cd47c1c8849a4b4a87748e6979=**********; _uetvid=b10b4790fb1d11ef85d7d1f1282b89c8; _gcl_au=1.1.**********.**********; _ga_NTLDK3J296=GS2.1.s1750125012$o45$g1$t1750125015$j57$l0$h0; acw_tc=0a03834117528177810158739e5fd57bb418bd8e9a1b5d4326bb25a4c1897a; sharedDoc=VX-9JZV2XW1e8JDqtAqvhpqE-b0aLDDSu3_1Fu36-VPoOzKspuCpMS098Invs1Uo; Hm_lvt_56f20ef8b9cc36436162ef11afabac21=**********; Hm_lpvt_56f20ef8b9cc36436162ef11afabac21=**********; HMACCOUNT=19BC3957598376B9; isTracking=true",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
    
    def fetch_url_content(self, url: str, timeout: int = 30) -> Optional[str]:
        """
        获取URL内容
        :param url: 要获取的URL
        :param timeout: 超时时间（秒）
        :return: 页面内容或None
        """
        try:
            response = requests.get(url, headers=self.headers, timeout=timeout, verify=False)
            if response.status_code == 200:
                return response.text
            else:
                print(f"❌ 请求失败，状态码：{response.status_code} - {url}")
                return None
        except RequestException as e:
            print(f"❌ 请求异常：{str(e)} - {url}")
            return None
    
    def extract_yaml_from_markdown(self, md_content: str) -> Optional[str]:
        """
        从Markdown中提取YAML代码块
        :param md_content: Markdown内容
        :return: YAML内容或None
        """
        pattern = r'```yaml\n(.*?)```'
        matches = re.findall(pattern, md_content, re.DOTALL)
        return matches[0].strip() if matches else None
    
    def _check_schema_properties(self, schema: Dict[str, Any], missing_list: List[Dict], 
                                context_path: str, api_path: str = None, 
                                api_summary: str = None) -> None:
        """
        递归检查Schema属性的注释
        :param schema: Schema定义
        :param missing_list: 缺失注释的参数列表
        :param context_path: 上下文路径
        :param api_path: API路径
        :param api_summary: API说明
        """
        if 'properties' not in schema:
            return
            
        for prop_name, prop_schema in schema['properties'].items():
            # 检查当前属性是否有描述
            if 'description' not in prop_schema or not prop_schema['description'].strip():
                missing_info = {
                    "参数名称": prop_name,
                    "参数位置": context_path
                }
                if api_path:
                    missing_info["接口路径"] = api_path
                if api_summary:
                    missing_info["接口说明"] = api_summary
                missing_list.append(missing_info)
            
            # 递归检查嵌套对象
            if 'properties' in prop_schema:
                self._check_schema_properties(
                    prop_schema, missing_list, 
                    f"{context_path}.{prop_name}", 
                    api_path, api_summary
                )
            elif 'items' in prop_schema:  # 处理数组
                if isinstance(prop_schema['items'], dict):
                    self._check_schema_properties(
                        prop_schema['items'], missing_list, 
                        f"{context_path}.{prop_name}[]", 
                        api_path, api_summary
                    )
    
    def validate_api_descriptions(self, openapi_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        验证OpenAPI文档中所有参数的description是否存在
        :param openapi_data: OpenAPI 3.0格式的数据
        :return: 缺失注释的参数列表
        """
        missing_descriptions = []
        
        # 遍历所有接口路径
        for path, path_item in openapi_data.get('paths', {}).items():
            for method, operation in path_item.items():
                if method not in ['get', 'post', 'put', 'delete', 'patch']:
                    continue
                
                summary = operation.get('summary', '未命名接口')
                
                # 检查路径参数和查询参数
                for param in operation.get('parameters', []):
                    # 跳过header参数（通常是认证相关）
                    if param.get('in') == 'header':
                        continue
                    
                    if 'description' not in param or not param['description'].strip():
                        missing_descriptions.append({
                            "接口路径": path,
                            "接口说明": summary,
                            "参数位置": f"{param.get('in', 'unknown')}参数",
                            "参数名称": param.get('name', 'unknown'),
                        })
                
                # 检查请求体Schema
                request_body = operation.get('requestBody', {})
                content = request_body.get('content', {})
                for media_type, media_content in content.items():
                    schema = media_content.get('schema', {})
                    if '$ref' in schema:
                        # 处理引用
                        ref_path = schema['$ref'].split('/')[-1]
                        if 'components' in openapi_data and 'schemas' in openapi_data['components']:
                            ref_schema = openapi_data['components']['schemas'].get(ref_path, {})
                            self._check_schema_properties(
                                ref_schema, missing_descriptions,
                                f"请求体({media_type})",
                                path, summary
                            )
                    else:
                        self._check_schema_properties(
                            schema, missing_descriptions,
                            f"请求体({media_type})",
                            path, summary
                        )
                
                # 检查响应Schema
                for status_code, response in operation.get('responses', {}).items():
                    if status_code.startswith('2'):  # 只检查成功响应
                        response_content = response.get('content', {})
                        for media_type, media_content in response_content.items():
                            schema = media_content.get('schema', {})
                            if '$ref' in schema:
                                ref_path = schema['$ref'].split('/')[-1]
                                if 'components' in openapi_data and 'schemas' in openapi_data['components']:
                                    ref_schema = openapi_data['components']['schemas'].get(ref_path, {})
                                    self._check_schema_properties(
                                        ref_schema, missing_descriptions,
                                        f"响应体({media_type})",
                                        path, summary
                                    )
                            else:
                                self._check_schema_properties(
                                    schema, missing_descriptions,
                                    f"响应体({media_type})",
                                    path, summary
                                )
        
        # 检查components/schemas中的独立Schema定义
        components = openapi_data.get('components', {})
        schemas = components.get('schemas', {})
        for schema_name, schema in schemas.items():
            # 跳过通用响应类型
            if schema_name in ['AjaxResult', 'CommonResult', 'Result']:
                continue
            self._check_schema_properties(
                schema, missing_descriptions,
                f"Schema定义({schema_name})",
                None, None
            )
        
        return missing_descriptions
    
    def extract_api_links_from_markdown(self, md_content: str) -> List[tuple]:
        """
        从Markdown文档中提取API链接
        :param md_content: Markdown内容
        :return: (接口名称, URL)的元组列表
        """
        # 匹配格式：- 接口名称 [描述](URL):
        pattern = r"- (.*?)\[(.*?)\]\((https?://[^\s<>]+)\):"
        matches = re.findall(pattern, md_content)
        return [(f"{name.strip()} - {desc.strip()}", url) for name, desc, url in matches]
    
    def check_api_list(self, api_list_url: str) -> Dict[str, Any]:
        """
        检查API列表中所有接口的注释完整性
        :param api_list_url: API列表的URL
        :return: 检查结果
        """
        print(f"🔍 开始检查API列表: {api_list_url}")
        
        # 获取API列表内容
        list_content = self.fetch_url_content(api_list_url)
        if not list_content:
            return {"error": "无法获取API列表内容"}
        
        # 提取API链接
        api_links = self.extract_api_links_from_markdown(list_content)
        if not api_links:
            return {"error": "未找到API链接"}
        
        print(f"📋 找到 {len(api_links)} 个API接口")
        
        results = {
            "total_apis": len(api_links),
            "checked_apis": 0,
            "apis_with_missing_comments": 0,
            "all_missing_comments": [],
            "api_details": []
        }
        
        for api_name, api_url in api_links:
            print(f"🔍 检查接口: {api_name}")
            
            # 获取接口详情
            api_content = self.fetch_url_content(api_url)
            if not api_content:
                print(f"❌ 无法获取接口内容: {api_name}")
                continue
            
            # 提取YAML内容
            yaml_content = self.extract_yaml_from_markdown(api_content)
            if not yaml_content:
                print(f"❌ 未找到YAML内容: {api_name}")
                continue
            
            try:
                # 解析YAML
                openapi_data = yaml.safe_load(yaml_content)
                
                # 检查注释
                missing_comments = self.validate_api_descriptions(openapi_data)
                
                results["checked_apis"] += 1
                
                api_detail = {
                    "name": api_name,
                    "url": api_url,
                    "missing_comments_count": len(missing_comments),
                    "missing_comments": missing_comments
                }
                
                if missing_comments:
                    results["apis_with_missing_comments"] += 1
                    results["all_missing_comments"].extend(missing_comments)
                    print(f"⚠️  发现 {len(missing_comments)} 个缺失注释的参数")
                else:
                    print(f"✅ 注释完整")
                
                results["api_details"].append(api_detail)
                
            except yaml.YAMLError as e:
                print(f"❌ YAML解析错误: {api_name} - {str(e)}")
                continue
        
        return results
    
    def print_summary_report(self, results: Dict[str, Any]) -> None:
        """
        打印汇总报告
        :param results: 检查结果
        """
        print("\n" + "="*80)
        print("📊 API注释完整性检查报告")
        print("="*80)
        
        if "error" in results:
            print(f"❌ 检查失败: {results['error']}")
            return
        
        print(f"📋 总接口数: {results['total_apis']}")
        print(f"✅ 已检查接口数: {results['checked_apis']}")
        print(f"⚠️  有缺失注释的接口数: {results['apis_with_missing_comments']}")
        print(f"📝 缺失注释参数总数: {len(results['all_missing_comments'])}")
        
        if results['apis_with_missing_comments'] > 0:
            print(f"\n⚠️  注释完整率: {((results['checked_apis'] - results['apis_with_missing_comments']) / results['checked_apis'] * 100):.1f}%")
            
            print("\n📋 缺失注释详情:")
            print("-" * 80)
            
            for api_detail in results['api_details']:
                if api_detail['missing_comments']:
                    print(f"\n🔸 接口: {api_detail['name']}")
                    print(f"   URL: {api_detail['url']}")
                    print(f"   缺失注释数: {api_detail['missing_comments_count']}")
                    
                    for missing in api_detail['missing_comments']:
                        print(f"   - 参数: {missing.get('参数名称', 'N/A')}")
                        print(f"     位置: {missing.get('参数位置', 'N/A')}")
                        if missing.get('接口路径'):
                            print(f"     路径: {missing.get('接口路径')}")
                        print()
        else:
            print("\n🎉 所有接口的注释都很完整！")


def main():
    """主函数"""
    # API列表URL
    api_list_url = "https://s.apifox.cn/96438ace-5a09-4ed0-bf36-d83b44739051/llms.txt"
    
    # 创建检查器
    checker = ApiCommentChecker()
    
    # 执行检查
    results = checker.check_api_list(api_list_url)
    
    # 打印报告
    checker.print_summary_report(results)
    
    # 保存详细结果到JSON文件
    with open('api_comment_check_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: api_comment_check_results.json")


if __name__ == "__main__":
    main()
