#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSK项目Git分析工具 - 主入口

统一的命令行入口，支持多种分析模式
"""

import sys
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import HSKProjectSettings, Settings
from analyzers.hsk_analyzer import HSKAnalyzer
from analyzers.quick_analyzer import QuickAnalyzer
from reports.report_converter import ReportConverter
from utils.date_utils import DateUtils

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="HSK项目Git分析工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --mode quick                    # 快速分析最近7天
  %(prog)s --mode weekly                   # 周分析
  %(prog)s --mode monthly                  # 月分析
  %(prog)s --mode custom --start 2024-07-01 --end 2024-08-12
  %(prog)s --mode branch --branch test --days 30
  %(prog)s --convert                       # 转换现有JSON报告为Markdown
        """
    )
    
    parser.add_argument(
        "--version", 
        action="version", 
        version=f"HSK Git分析工具 v{Settings.VERSION}"
    )
    
    parser.add_argument(
        "--mode", 
        choices=["quick", "weekly", "monthly", "custom", "branch", "interactive"],
        default="interactive",
        help="分析模式 (默认: interactive)"
    )
    
    parser.add_argument(
        "--repo", 
        default=HSKProjectSettings.PROJECT_PATH,
        help=f"Git仓库路径 (默认: {HSKProjectSettings.PROJECT_PATH})"
    )
    
    parser.add_argument(
        "--start", 
        help="开始日期 (YYYY-MM-DD格式)"
    )
    
    parser.add_argument(
        "--end", 
        help="结束日期 (YYYY-MM-DD格式)"
    )
    
    parser.add_argument(
        "--branch", 
        help="指定分析的分支"
    )
    
    parser.add_argument(
        "--days", 
        type=int, 
        default=7,
        help="分析最近几天 (默认: 7)"
    )
    
    parser.add_argument(
        "--output", 
        help="输出文件路径"
    )
    
    parser.add_argument(
        "--convert", 
        action="store_true",
        help="转换现有JSON报告为Markdown格式"
    )
    
    parser.add_argument(
        "--quick", 
        action="store_true",
        help="使用快速分析器 (适合大型项目)"
    )
    
    return parser

def validate_arguments(args):
    """验证命令行参数"""
    errors = []
    
    # 验证仓库路径
    if not Path(args.repo).exists():
        errors.append(f"仓库路径不存在: {args.repo}")
    
    # 验证日期格式
    if args.start and not DateUtils.validate_date_format(args.start):
        errors.append(f"开始日期格式错误: {args.start} (应为 YYYY-MM-DD)")
    
    if args.end and not DateUtils.validate_date_format(args.end):
        errors.append(f"结束日期格式错误: {args.end} (应为 YYYY-MM-DD)")
    
    # 验证自定义模式的参数
    if args.mode == "custom":
        if not args.start or not args.end:
            errors.append("自定义模式需要指定 --start 和 --end 参数")
    
    return errors

def run_analysis(args):
    """执行分析"""
    print("🚀 HSK项目Git分析工具")
    print("="*50)
    
    # 选择分析器
    if args.quick:
        analyzer = QuickAnalyzer(args.repo)
        print("使用快速分析器")
    else:
        analyzer = HSKAnalyzer(args.repo)
        print("使用标准分析器")
    
    # 执行分析
    result = None
    
    if args.mode == "quick" or args.mode == "weekly":
        result = analyzer.analyze_recent_days(7, args.branch)
    elif args.mode == "monthly":
        result = analyzer.analyze_recent_days(30, args.branch)
    elif args.mode == "custom":
        result = analyzer.analyze_date_range(args.start, args.end, args.branch)
    elif args.mode == "branch":
        result = analyzer.analyze_recent_days(args.days, args.branch)
    
    if not result:
        print("❌ 分析失败")
        return False
    
    # 打印摘要
    analyzer.print_summary(result)
    
    # 保存报告
    if args.output:
        output_file = args.output
    else:
        # 生成默认文件名
        if args.mode == "custom":
            start_date, end_date = args.start, args.end
        else:
            start_date, end_date = DateUtils.get_recent_date_range(args.days)
        
        output_file = HSKProjectSettings.get_hsk_report_filename(
            start_date, end_date, "json", args.branch
        )
        output_file = str(Path(Settings.get_output_dir()) / output_file)
    
    analyzer.save_report(result, output_file)
    print(f"\n📁 报告已保存到: {output_file}")
    
    return True

def run_convert(args):
    """执行报告转换"""
    print("📝 报告格式转换")
    print("="*30)
    
    converter = ReportConverter()
    
    if converter.find_and_convert_latest():
        print("✅ 转换完成")
        return True
    else:
        print("❌ 转换失败")
        return False

def run_interactive():
    """运行交互式模式"""
    print("🚀 HSK项目Git分析工具 - 交互式模式")
    print("="*50)
    
    # 这里可以导入并运行现有的交互式分析器
    try:
        from scripts.run_hsk_analyzer import main as hsk_main
        hsk_main()
    except ImportError:
        print("❌ 无法启动交互式模式")
        print("请使用命令行参数或直接运行 scripts/run_hsk_analyzer.py")

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 验证参数
    errors = validate_arguments(args)
    if errors:
        print("❌ 参数错误:")
        for error in errors:
            print(f"  - {error}")
        return 1
    
    try:
        # 执行相应的操作
        if args.convert:
            success = run_convert(args)
        elif args.mode == "interactive":
            run_interactive()
            return 0
        else:
            success = run_analysis(args)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
        return 1
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
