#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试接口识别问题
专门检查为什么某些接口没有被识别
"""

import os
import subprocess
import re
from pathlib import Path

def get_file_content_at_commit(repo_path, commit_hash, file_path):
    """获取指定提交时的文件内容"""
    try:
        result = subprocess.run(
            ['git', 'show', f'{commit_hash}:{file_path}'],
            cwd=repo_path,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=10
        )
        if result.returncode == 0:
            return result.stdout
        else:
            print(f"❌ 获取文件内容失败: {result.stderr}")
            return ""
    except Exception as e:
        print(f"❌ 获取文件内容异常: {e}")
        return ""

def analyze_controller_file(content, file_path):
    """分析Controller文件中的接口"""
    print(f"\n🔍 分析文件: {file_path}")
    print("="*80)
    
    lines = content.split('\n')
    
    # 检查是否是Controller
    is_controller = False
    class_name = ""
    base_path = ""
    
    controller_patterns = [
        r'@RestController',
        r'@Controller',
        r'@RequestMapping'
    ]
    
    for i, line in enumerate(lines):
        # 检查Controller注解
        for pattern in controller_patterns:
            if re.search(pattern, line):
                is_controller = True
                print(f"✅ 找到Controller注解: {pattern} (第{i+1}行)")
                break
        
        # 提取类名
        class_match = re.search(r'class\s+(\w+)', line)
        if class_match:
            class_name = class_match.group(1)
            print(f"✅ 找到类名: {class_name} (第{i+1}行)")
        
        # 提取类级别的RequestMapping路径
        if '@RequestMapping' in line:
            path_match = re.search(r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']', line)
            if path_match:
                base_path = path_match.group(1)
                print(f"✅ 找到基础路径: {base_path} (第{i+1}行)")
    
    if not is_controller:
        print("❌ 不是Controller文件")
        return []
    
    print(f"\n📋 Controller信息:")
    print(f"  类名: {class_name}")
    print(f"  基础路径: {base_path}")
    
    # 查找HTTP方法注解
    http_patterns = {
        'GET': [r'@GetMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.GET'],
        'POST': [r'@PostMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.POST'],
        'PUT': [r'@PutMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PUT'],
        'DELETE': [r'@DeleteMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.DELETE'],
        'PATCH': [r'@PatchMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PATCH']
    }
    
    interfaces = []
    
    print(f"\n🔗 查找接口:")
    for i, line in enumerate(lines):
        for method, patterns in http_patterns.items():
            for pattern in patterns:
                if re.search(pattern, line):
                    print(f"  第{i+1}行: {line.strip()}")
                    
                    # 提取路径
                    path = ""
                    path_match = re.search(r'["\']([^"\']+)["\']', line)
                    if path_match:
                        path = path_match.group(1)
                    
                    # 查找下一个方法定义
                    method_name = ""
                    for j in range(i + 1, min(i + 10, len(lines))):
                        method_match = re.search(r'public\s+\w+\s+(\w+)\s*\(', lines[j])
                        if method_match:
                            method_name = method_match.group(1)
                            print(f"    -> 方法名: {method_name} (第{j+1}行)")
                            break
                    
                    # 构建完整路径
                    full_path = base_path + path if base_path else path
                    
                    interface_info = {
                        'method': method,
                        'path': full_path,
                        'method_name': method_name,
                        'line': i + 1
                    }
                    interfaces.append(interface_info)
                    
                    print(f"    ✅ 识别接口: {method} {full_path}")
                    break
    
    return interfaces

def debug_specific_commit():
    """调试特定提交"""
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    
    # 你提到的提交
    commit_hash = "81f7c9b7"
    
    print(f"🔍 调试提交: {commit_hash}")
    print("="*80)
    
    # 获取这个提交的变更文件
    try:
        result = subprocess.run(
            ['git', 'show', '--name-status', '--format=', commit_hash],
            cwd=HSK_PROJECT_PATH,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=10
        )
        
        if result.returncode == 0:
            print("📁 变更的文件:")
            java_files = []
            for line in result.stdout.strip().split('\n'):
                if line and '\t' in line:
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        status = parts[0]
                        file_path = parts[1]
                        print(f"  {status} {file_path}")
                        if file_path.endswith('.java'):
                            java_files.append((status, file_path))
            
            # 分析每个Java文件
            for status, file_path in java_files:
                if 'Controller' in file_path:
                    print(f"\n🎯 分析Controller文件: {file_path}")
                    content = get_file_content_at_commit(HSK_PROJECT_PATH, commit_hash, file_path)
                    if content:
                        interfaces = analyze_controller_file(content, file_path)
                        
                        if interfaces:
                            print(f"\n✅ 在此文件中找到 {len(interfaces)} 个接口:")
                            for interface in interfaces:
                                print(f"  - {interface['method']} {interface['path']} ({interface['method_name']})")
                        else:
                            print(f"\n❌ 在此文件中没有找到接口")
                    else:
                        print(f"❌ 无法获取文件内容")
        else:
            print(f"❌ 获取提交信息失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 调试异常: {e}")

def test_interface_patterns():
    """测试接口识别模式"""
    print("🧪 测试接口识别模式")
    print("="*80)
    
    test_cases = [
        '@PostMapping("/getQuestionDetails")',
        '@PostMapping("/v1/getCourseHourVideo")',
        '@GetMapping("/test")',
        '@RequestMapping(value = "/api/test", method = RequestMethod.POST)',
        '@PostMapping(value = "/edu/question/getQuestionDetails")',
    ]
    
    http_patterns = {
        'GET': [r'@GetMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.GET'],
        'POST': [r'@PostMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.POST'],
        'PUT': [r'@PutMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PUT'],
        'DELETE': [r'@DeleteMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.DELETE'],
        'PATCH': [r'@PatchMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PATCH']
    }
    
    for test_case in test_cases:
        print(f"\n测试: {test_case}")
        found = False
        
        for method, patterns in http_patterns.items():
            for pattern in patterns:
                if re.search(pattern, test_case):
                    print(f"  ✅ 匹配 {method} 模式: {pattern}")
                    
                    # 提取路径
                    path_match = re.search(r'["\']([^"\']+)["\']', test_case)
                    if path_match:
                        path = path_match.group(1)
                        print(f"  ✅ 提取路径: {path}")
                    else:
                        print(f"  ❌ 无法提取路径")
                    
                    found = True
                    break
            if found:
                break
        
        if not found:
            print(f"  ❌ 没有匹配任何模式")

def main():
    """主函数"""
    print("🐛 接口识别调试工具")
    print("="*80)
    
    print("\n1. 测试接口识别模式")
    test_interface_patterns()
    
    print("\n2. 调试特定提交")
    debug_specific_commit()

if __name__ == "__main__":
    main()
