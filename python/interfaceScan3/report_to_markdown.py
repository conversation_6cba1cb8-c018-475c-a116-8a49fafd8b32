#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将JSON分析报告转换为Markdown格式
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

def load_json_report(file_path):
    """加载JSON报告"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载JSON文件失败: {e}")
        return None

def generate_markdown_report(report_data, output_file):
    """生成Markdown报告"""
    
    # 获取报告基本信息
    summary = report_data.get('summary', {})
    interfaces = report_data.get('interfaces', [])
    entities = report_data.get('entities', [])
    
    # 生成Markdown内容
    markdown_content = []
    
    # 标题和基本信息
    markdown_content.append("# HSK项目Git分析报告")
    markdown_content.append("")
    markdown_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    markdown_content.append("")
    
    # 总体统计
    markdown_content.append("## 📊 总体统计")
    markdown_content.append("")
    markdown_content.append("| 类型 | 总数 | 新增 | 修改 | 删除 |")
    markdown_content.append("|------|------|------|------|------|")
    markdown_content.append(f"| 🔗 HTTP接口 | {summary.get('total_interfaces', 0)} | {summary.get('new_interfaces', 0)} | {summary.get('modified_interfaces', 0)} | {summary.get('deleted_interfaces', 0)} |")
    markdown_content.append(f"| 🗃️ 数据库实体 | {summary.get('total_entities', 0)} | {summary.get('new_entities', 0)} | {summary.get('modified_entities', 0)} | {summary.get('deleted_entities', 0)} |")
    markdown_content.append("")
    
    # 新增接口详情
    new_interfaces = [i for i in interfaces if i.get('operation') == 'ADD']
    if new_interfaces:
        markdown_content.append("## 🆕 新增接口详情")
        markdown_content.append("")
        markdown_content.append("| HTTP方法 | 接口路径 | 控制器类 | 方法名 | 提交信息 |")
        markdown_content.append("|----------|----------|----------|--------|----------|")
        
        for interface in new_interfaces:
            method = interface.get('method', '')
            path = interface.get('path', '')
            class_name = interface.get('class_name', '')
            method_name = interface.get('method_name', '')
            commit_hash = interface.get('commit_hash', '')[:8]
            commit_date = interface.get('commit_date', '')[:10]
            file_path = interface.get('file_path', '').split('/')[-1]  # 只显示文件名
            
            markdown_content.append(f"| `{method}` | `{path}` | `{class_name}` | `{method_name}` | `{commit_hash}` ({commit_date}) |")
        
        markdown_content.append("")
    
    # 修改接口详情
    modified_interfaces = [i for i in interfaces if i.get('operation') == 'MODIFY']
    if modified_interfaces:
        markdown_content.append("## ✏️ 修改接口详情")
        markdown_content.append("")
        markdown_content.append("| HTTP方法 | 接口路径 | 控制器类 | 方法名 | 提交信息 |")
        markdown_content.append("|----------|----------|----------|--------|----------|")
        
        for interface in modified_interfaces:
            method = interface.get('method', '')
            path = interface.get('path', '')
            class_name = interface.get('class_name', '')
            method_name = interface.get('method_name', '')
            commit_hash = interface.get('commit_hash', '')[:8]
            commit_date = interface.get('commit_date', '')[:10]
            
            markdown_content.append(f"| `{method}` | `{path}` | `{class_name}` | `{method_name}` | `{commit_hash}` ({commit_date}) |")
        
        markdown_content.append("")
    
    # 新增实体详情
    new_entities = [e for e in entities if e.get('operation') == 'ADD']
    if new_entities:
        markdown_content.append("## 🆕 新增数据库实体详情")
        markdown_content.append("")
        markdown_content.append("| 表名 | 实体类名 | 字段列表 | 提交信息 |")
        markdown_content.append("|------|----------|----------|----------|")
        
        for entity in new_entities:
            table_name = entity.get('table_name', '')
            class_name = entity.get('class_name', '')
            fields = entity.get('fields', [])
            fields_str = ', '.join(fields[:5])  # 只显示前5个字段
            if len(fields) > 5:
                fields_str += f" ... (共{len(fields)}个字段)"
            commit_hash = entity.get('commit_hash', '')[:8]
            commit_date = entity.get('commit_date', '')[:10]
            
            markdown_content.append(f"| `{table_name}` | `{class_name}` | {fields_str} | `{commit_hash}` ({commit_date}) |")
        
        markdown_content.append("")
    
    # 修改实体详情
    modified_entities = [e for e in entities if e.get('operation') == 'MODIFY']
    if modified_entities:
        markdown_content.append("## ✏️ 修改数据库实体详情")
        markdown_content.append("")
        markdown_content.append("| 表名 | 实体类名 | 字段列表 | 提交信息 |")
        markdown_content.append("|------|----------|----------|----------|")
        
        for entity in modified_entities:
            table_name = entity.get('table_name', '')
            class_name = entity.get('class_name', '')
            fields = entity.get('fields', [])
            fields_str = ', '.join(fields[:5])  # 只显示前5个字段
            if len(fields) > 5:
                fields_str += f" ... (共{len(fields)}个字段)"
            commit_hash = entity.get('commit_hash', '')[:8]
            commit_date = entity.get('commit_date', '')[:10]
            
            markdown_content.append(f"| `{table_name}` | `{class_name}` | {fields_str} | `{commit_hash}` ({commit_date}) |")
        
        markdown_content.append("")
    
    # 按文件分组的详细信息
    if interfaces or entities:
        markdown_content.append("## 📁 按文件分组的变更详情")
        markdown_content.append("")
        
        # 收集所有文件
        all_files = set()
        for item in interfaces + entities:
            all_files.add(item.get('file_path', ''))
        
        for file_path in sorted(all_files):
            if not file_path:
                continue
                
            file_interfaces = [i for i in interfaces if i.get('file_path') == file_path]
            file_entities = [e for e in entities if e.get('file_path') == file_path]
            
            if file_interfaces or file_entities:
                # 文件名作为子标题
                file_name = file_path.split('/')[-1]
                markdown_content.append(f"### 📄 {file_name}")
                markdown_content.append("")
                markdown_content.append(f"**文件路径**: `{file_path}`")
                markdown_content.append("")
                
                if file_interfaces:
                    markdown_content.append("**接口变更**:")
                    for interface in file_interfaces:
                        operation_emoji = {"ADD": "➕", "MODIFY": "✏️", "DELETE": "❌"}.get(interface.get('operation'), "🔄")
                        markdown_content.append(f"- {operation_emoji} `{interface.get('method')} {interface.get('path')}` ({interface.get('method_name')})")
                    markdown_content.append("")
                
                if file_entities:
                    markdown_content.append("**实体变更**:")
                    for entity in file_entities:
                        operation_emoji = {"ADD": "➕", "MODIFY": "✏️", "DELETE": "❌"}.get(entity.get('operation'), "🔄")
                        markdown_content.append(f"- {operation_emoji} 表 `{entity.get('table_name')}` (类: `{entity.get('class_name')}`)")
                    markdown_content.append("")
    
    # 技术说明
    markdown_content.append("## 📋 技术说明")
    markdown_content.append("")
    markdown_content.append("### 分析范围")
    markdown_content.append("- **HTTP接口**: 识别Spring Boot项目中的`@RestController`、`@Controller`注解的类")
    markdown_content.append("- **HTTP方法**: 支持`@GetMapping`、`@PostMapping`、`@PutMapping`、`@DeleteMapping`、`@PatchMapping`等注解")
    markdown_content.append("- **数据库实体**: 识别MyBatis Plus项目中的`@Entity`、`@Table`、`@TableName`注解的类")
    markdown_content.append("")
    markdown_content.append("### 变更类型说明")
    markdown_content.append("- **新增(ADD)**: 在Git提交中新增的文件")
    markdown_content.append("- **修改(MODIFY)**: 在Git提交中修改的文件")
    markdown_content.append("- **删除(DELETE)**: 在Git提交中删除的文件")
    markdown_content.append("")
    markdown_content.append("---")
    markdown_content.append("")
    markdown_content.append("*本报告由HSK项目Git分析工具自动生成*")
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        return True
    except Exception as e:
        print(f"❌ 写入Markdown文件失败: {e}")
        return False

def main():
    """主函数"""
    print("📝 JSON报告转Markdown工具")
    print("="*50)
    
    # 查找JSON报告文件
    current_dir = Path(__file__).parent
    json_files = list(current_dir.glob("hsk_analysis_*.json"))
    
    if not json_files:
        print("❌ 未找到分析报告文件")
        print("请确保在同一目录下有 hsk_analysis_*.json 文件")
        return
    
    # 如果有多个文件，选择最新的
    if len(json_files) > 1:
        print(f"📁 找到 {len(json_files)} 个报告文件:")
        for i, file in enumerate(json_files, 1):
            print(f"  {i}. {file.name}")
        
        while True:
            try:
                choice = input(f"\n请选择要转换的文件 (1-{len(json_files)}): ").strip()
                if choice == "":
                    # 默认选择最新的文件
                    json_file = max(json_files, key=lambda f: f.stat().st_mtime)
                    break
                
                choice_num = int(choice)
                if 1 <= choice_num <= len(json_files):
                    json_file = json_files[choice_num - 1]
                    break
                else:
                    print("❌ 无效选择，请重新输入")
            except ValueError:
                print("❌ 请输入数字")
    else:
        json_file = json_files[0]
    
    print(f"📄 选择文件: {json_file.name}")
    
    # 加载JSON报告
    report_data = load_json_report(json_file)
    if not report_data:
        return
    
    # 生成输出文件名
    output_file = json_file.with_suffix('.md')
    
    print(f"🔄 正在转换为Markdown格式...")
    
    # 生成Markdown报告
    if generate_markdown_report(report_data, output_file):
        print(f"✅ Markdown报告生成成功!")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 统计信息:")
        
        summary = report_data.get('summary', {})
        print(f"  - 总接口数: {summary.get('total_interfaces', 0)}")
        print(f"  - 新增接口: {summary.get('new_interfaces', 0)}")
        print(f"  - 总实体数: {summary.get('total_entities', 0)}")
        print(f"  - 新增实体: {summary.get('new_entities', 0)}")
    else:
        print("❌ Markdown报告生成失败")

if __name__ == "__main__":
    main()
