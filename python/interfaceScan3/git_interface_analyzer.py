#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Git接口和实体分析工具 - 主分析器
用于分析Java Spring项目中指定时间段内的HTTP接口和数据库实体变化

这个文件将被移动到 core/analyzer.py
"""

import os
import re
import json
import subprocess
from datetime import datetime
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# 注意：这个文件将被重构，请使用新的模块化结构

@dataclass
class HttpInterface:
    """HTTP接口信息"""
    method: str  # GET, POST, PUT, DELETE等
    path: str    # 接口路径
    class_name: str  # 所在类名
    method_name: str  # 方法名
    file_path: str   # 文件路径
    commit_hash: str  # 提交哈希
    commit_date: str  # 提交日期
    operation: str    # ADD, MODIFY, DELETE

@dataclass
class DatabaseEntity:
    """数据库实体信息"""
    table_name: str   # 表名
    class_name: str   # 实体类名
    file_path: str    # 文件路径
    fields: List[str] # 字段列表
    commit_hash: str  # 提交哈希
    commit_date: str  # 提交日期
    operation: str    # ADD, MODIFY, DELETE

class GitInterfaceAnalyzer:
    """Git接口分析器"""
    
    def __init__(self, repo_path: str):
        self.repo_path = Path(repo_path)
        self.interfaces: List[HttpInterface] = []
        self.entities: List[DatabaseEntity] = []
        
        # Spring注解模式
        self.controller_patterns = [
            r'@RestController',
            r'@Controller',
            r'@RequestMapping'
        ]
        
        # HTTP方法注解模式
        self.http_method_patterns = {
            'GET': [r'@GetMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.GET'],
            'POST': [r'@PostMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.POST'],
            'PUT': [r'@PutMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PUT'],
            'DELETE': [r'@DeleteMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.DELETE'],
            'PATCH': [r'@PatchMapping', r'@RequestMapping.*method\s*=\s*RequestMethod\.PATCH']
        }
        
        # MyBatis Plus实体注解模式
        self.entity_patterns = [
            r'@Entity',
            r'@Table',
            r'@TableName'
        ]
        
        # 字段注解模式
        self.field_patterns = [
            r'@Column',
            r'@TableField',
            r'@TableId'
        ]

    def run_git_command(self, command: List[str]) -> str:
        """执行Git命令"""
        try:
            result = subprocess.run(
                command,
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            if result.returncode != 0:
                print(f"Git命令执行失败: {' '.join(command)}")
                print(f"错误信息: {result.stderr}")
                return ""
            return result.stdout
        except Exception as e:
            print(f"执行Git命令时出错: {e}")
            return ""

    def get_commits_in_range(self, start_date: str, end_date: str, branch: str = None) -> List[Tuple[str, str]]:
        """获取指定时间范围内的提交"""
        command = [
            'git', 'log',
            f'--since={start_date}',
            f'--until={end_date}',
            '--pretty=format:%H|%ci',
            '--reverse'
        ]

        # 如果指定了分支，添加分支参数
        if branch:
            command.append(branch)

        output = self.run_git_command(command)
        commits = []

        for line in output.strip().split('\n'):
            if line and '|' in line:
                commit_hash, commit_date = line.split('|', 1)
                commits.append((commit_hash.strip(), commit_date.strip()))

        return commits

    def get_changed_files(self, commit_hash: str) -> List[Tuple[str, str]]:
        """获取指定提交中变更的Java文件"""
        command = ['git', 'show', '--name-status', '--format=', commit_hash]
        output = self.run_git_command(command)

        changed_files = []
        for line in output.strip().split('\n'):
            if line and '\t' in line:
                parts = line.split('\t')
                if len(parts) >= 2:
                    status = parts[0]
                    file_path = parts[1]
                    if file_path.endswith('.java'):
                        changed_files.append((status, file_path))

        return changed_files

    def get_file_content_at_commit(self, commit_hash: str, file_path: str) -> str:
        """获取指定提交时的文件内容"""
        command = ['git', 'show', f'{commit_hash}:{file_path}']
        return self.run_git_command(command)

    def extract_interfaces_from_content(self, content: str, file_path: str, 
                                      commit_hash: str, commit_date: str, 
                                      operation: str) -> List[HttpInterface]:
        """从文件内容中提取HTTP接口"""
        interfaces = []
        lines = content.split('\n')
        
        # 检查是否是Controller类
        is_controller = False
        class_name = ""
        class_base_path = ""
        
        for i, line in enumerate(lines):
            # 检查Controller注解
            for pattern in self.controller_patterns:
                if re.search(pattern, line):
                    is_controller = True
                    break
            
            # 提取类名
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                class_name = class_match.group(1)
            
            # 提取类级别的RequestMapping路径
            if '@RequestMapping' in line:
                path_match = re.search(r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']', line)
                if path_match:
                    class_base_path = path_match.group(1)
        
        if not is_controller:
            return interfaces
        
        # 提取方法级别的接口
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # 检查HTTP方法注解
            for method, patterns in self.http_method_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line):
                        # 提取路径
                        path = ""
                        path_match = re.search(r'["\']([^"\']+)["\']', line)
                        if path_match:
                            path = path_match.group(1)
                        
                        # 查找下一个方法定义
                        method_name = ""
                        for j in range(i + 1, min(i + 10, len(lines))):
                            # 修复正则表达式以支持泛型返回类型
                            method_match = re.search(r'public\s+[^(]+\s+(\w+)\s*\(', lines[j])
                            if method_match:
                                method_name = method_match.group(1)
                                break
                        
                        # 构建完整路径
                        full_path = class_base_path + path if class_base_path else path
                        
                        if method_name:
                            interface = HttpInterface(
                                method=method,
                                path=full_path,
                                class_name=class_name,
                                method_name=method_name,
                                file_path=file_path,
                                commit_hash=commit_hash,
                                commit_date=commit_date,
                                operation=operation
                            )
                            interfaces.append(interface)
                        break
            i += 1
        
        return interfaces

    def extract_entities_from_content(self, content: str, file_path: str,
                                    commit_hash: str, commit_date: str,
                                    operation: str) -> List[DatabaseEntity]:
        """从文件内容中提取数据库实体"""
        entities = []
        lines = content.split('\n')
        
        # 检查是否是实体类
        is_entity = False
        class_name = ""
        table_name = ""
        fields = []
        
        for i, line in enumerate(lines):
            # 检查实体注解
            for pattern in self.entity_patterns:
                if re.search(pattern, line):
                    is_entity = True
                    
                    # 提取表名
                    if '@TableName' in line or '@Table' in line:
                        name_match = re.search(r'["\']([^"\']+)["\']', line)
                        if name_match:
                            table_name = name_match.group(1)
                    break
            
            # 提取类名
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                class_name = class_match.group(1)
                if not table_name:
                    # 如果没有显式指定表名，使用类名转换
                    table_name = self.camel_to_snake(class_name)
            
            # 提取字段
            if is_entity:
                # 检查字段注解
                for pattern in self.field_patterns:
                    if re.search(pattern, line):
                        # 查找下一行的字段定义
                        for j in range(i + 1, min(i + 5, len(lines))):
                            field_match = re.search(r'private\s+\w+\s+(\w+)\s*;', lines[j])
                            if field_match:
                                fields.append(field_match.group(1))
                                break
                        break
                
                # 直接匹配字段定义（没有注解的情况）
                field_match = re.search(r'private\s+\w+\s+(\w+)\s*;', line)
                if field_match:
                    field_name = field_match.group(1)
                    if field_name not in fields:
                        fields.append(field_name)
        
        if is_entity and class_name:
            entity = DatabaseEntity(
                table_name=table_name,
                class_name=class_name,
                file_path=file_path,
                fields=fields,
                commit_hash=commit_hash,
                commit_date=commit_date,
                operation=operation
            )
            entities.append(entity)
        
        return entities

    def camel_to_snake(self, name: str) -> str:
        """驼峰命名转下划线命名"""
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    def analyze_period(self, start_date: str, end_date: str, branch: str = None) -> Dict:
        """分析指定时间段的变化"""
        print(f"分析时间段: {start_date} 到 {end_date}")
        if branch:
            print(f"分析分支: {branch}")

        # 获取时间范围内的提交
        commits = self.get_commits_in_range(start_date, end_date, branch)
        print(f"找到 {len(commits)} 个提交")
        
        for commit_hash, commit_date in commits:
            print(f"分析提交: {commit_hash[:8]} ({commit_date})")
            
            # 获取变更的文件
            changed_files = self.get_changed_files(commit_hash)
            
            for status, file_path in changed_files:
                # 确定操作类型
                operation = "MODIFY"
                if status == "A":
                    operation = "ADD"
                elif status == "D":
                    operation = "DELETE"
                
                # 获取文件内容
                if operation != "DELETE":
                    content = self.get_file_content_at_commit(commit_hash, file_path)
                    if content:
                        # 提取接口
                        interfaces = self.extract_interfaces_from_content(
                            content, file_path, commit_hash, commit_date, operation
                        )
                        self.interfaces.extend(interfaces)
                        
                        # 提取实体
                        entities = self.extract_entities_from_content(
                            content, file_path, commit_hash, commit_date, operation
                        )
                        self.entities.extend(entities)
        
        # 生成分析报告
        return self.generate_report()

    def generate_report(self) -> Dict:
        """生成分析报告"""
        report = {
            "summary": {
                "total_interfaces": len(self.interfaces),
                "total_entities": len(self.entities),
                "new_interfaces": len([i for i in self.interfaces if i.operation == "ADD"]),
                "new_entities": len([e for e in self.entities if e.operation == "ADD"]),
                "modified_interfaces": len([i for i in self.interfaces if i.operation == "MODIFY"]),
                "modified_entities": len([e for e in self.entities if e.operation == "MODIFY"]),
                "deleted_interfaces": len([i for i in self.interfaces if i.operation == "DELETE"]),
                "deleted_entities": len([e for e in self.entities if e.operation == "DELETE"])
            },
            "interfaces": [asdict(interface) for interface in self.interfaces],
            "entities": [asdict(entity) for entity in self.entities]
        }
        
        return report

    def save_report(self, report: Dict, output_file: str):
        """保存分析报告"""
        # 保存JSON报告
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"JSON报告已保存到: {output_file}")

        # 自动生成Markdown报告
        try:
            markdown_file = output_file.replace('.json', '.md')
            self.generate_markdown_report(report, markdown_file)
            print(f"Markdown报告已保存到: {markdown_file}")
        except Exception as e:
            print(f"生成Markdown报告时出错: {e}")

    def generate_markdown_report(self, report_data: Dict, output_file: str):
        """生成Markdown报告"""
        from datetime import datetime

        # 获取报告基本信息
        summary = report_data.get('summary', {})
        interfaces = report_data.get('interfaces', [])
        entities = report_data.get('entities', [])

        # 生成Markdown内容
        markdown_content = []

        # 标题和基本信息
        markdown_content.append("# HSK项目Git分析报告")
        markdown_content.append("")
        markdown_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_content.append("")

        # 总体统计
        markdown_content.append("## 📊 总体统计")
        markdown_content.append("")
        markdown_content.append("| 类型 | 总数 | 新增 | 修改 | 删除 |")
        markdown_content.append("|------|------|------|------|------|")
        markdown_content.append(f"| 🔗 HTTP接口 | {summary.get('total_interfaces', 0)} | {summary.get('new_interfaces', 0)} | {summary.get('modified_interfaces', 0)} | {summary.get('deleted_interfaces', 0)} |")
        markdown_content.append(f"| 🗃️ 数据库实体 | {summary.get('total_entities', 0)} | {summary.get('new_entities', 0)} | {summary.get('modified_entities', 0)} | {summary.get('deleted_entities', 0)} |")
        markdown_content.append("")

        # 新增接口详情
        new_interfaces = [i for i in interfaces if i.get('operation') == 'ADD']
        if new_interfaces:
            markdown_content.append("## 🆕 新增接口详情")
            markdown_content.append("")
            markdown_content.append("| HTTP方法 | 接口路径 | 控制器类 | 方法名 | 提交信息 |")
            markdown_content.append("|----------|----------|----------|--------|----------|")

            for interface in new_interfaces:
                method = interface.get('method', '')
                path = interface.get('path', '')
                class_name = interface.get('class_name', '')
                method_name = interface.get('method_name', '')
                commit_hash = interface.get('commit_hash', '')[:8]
                commit_date = interface.get('commit_date', '')[:10]

                markdown_content.append(f"| `{method}` | `{path}` | `{class_name}` | `{method_name}` | `{commit_hash}` ({commit_date}) |")

            markdown_content.append("")

        # 新增实体详情
        new_entities = [e for e in entities if e.get('operation') == 'ADD']
        if new_entities:
            markdown_content.append("## 🆕 新增数据库实体详情")
            markdown_content.append("")
            markdown_content.append("| 表名 | 实体类名 | 字段列表 | 提交信息 |")
            markdown_content.append("|------|----------|----------|----------|")

            for entity in new_entities:
                table_name = entity.get('table_name', '')
                class_name = entity.get('class_name', '')
                fields = entity.get('fields', [])
                fields_str = ', '.join(fields[:5])  # 只显示前5个字段
                if len(fields) > 5:
                    fields_str += f" ... (共{len(fields)}个字段)"
                commit_hash = entity.get('commit_hash', '')[:8]
                commit_date = entity.get('commit_date', '')[:10]

                markdown_content.append(f"| `{table_name}` | `{class_name}` | {fields_str} | `{commit_hash}` ({commit_date}) |")

            markdown_content.append("")

        # 技术说明
        markdown_content.append("## 📋 技术说明")
        markdown_content.append("")
        markdown_content.append("### 分析范围")
        markdown_content.append("- **HTTP接口**: 识别Spring Boot项目中的`@RestController`、`@Controller`注解的类")
        markdown_content.append("- **HTTP方法**: 支持`@GetMapping`、`@PostMapping`、`@PutMapping`、`@DeleteMapping`、`@PatchMapping`等注解")
        markdown_content.append("- **数据库实体**: 识别MyBatis Plus项目中的`@Entity`、`@Table`、`@TableName`注解的类")
        markdown_content.append("")
        markdown_content.append("---")
        markdown_content.append("")
        markdown_content.append("*本报告由HSK项目Git分析工具自动生成*")

        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Git接口和实体分析工具')
    parser.add_argument('--repo', required=True, help='Git仓库路径')
    parser.add_argument('--start', required=True, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end', required=True, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--output', default='analysis_report.json', help='输出文件路径')
    
    args = parser.parse_args()
    
    analyzer = GitInterfaceAnalyzer(args.repo)
    report = analyzer.analyze_period(args.start, args.end)
    analyzer.save_report(report, args.output)
    
    # 打印摘要
    summary = report['summary']
    print("\n=== 分析摘要 ===")
    print(f"新增接口: {summary['new_interfaces']}")
    print(f"修改接口: {summary['modified_interfaces']}")
    print(f"删除接口: {summary['deleted_interfaces']}")
    print(f"新增实体: {summary['new_entities']}")
    print(f"修改实体: {summary['modified_entities']}")
    print(f"删除实体: {summary['deleted_entities']}")

if __name__ == "__main__":
    main()
