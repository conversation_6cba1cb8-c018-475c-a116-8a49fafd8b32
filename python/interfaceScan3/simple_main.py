#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的主入口 - 用于测试新的模块化结构
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_new_structure():
    """测试新的模块化结构"""
    print("🧪 测试新的模块化结构")
    print("="*50)
    
    try:
        # 测试核心模块
        print("📦 测试核心模块...")
        from core.models import HttpInterface, DatabaseEntity, AnalysisResult
        from core.patterns import PatternMatcher
        print("✅ 核心模块导入成功")
        
        # 测试工具模块
        print("🔧 测试工具模块...")
        from utils.git_utils import GitUtils
        from utils.date_utils import DateUtils
        from utils.file_utils import FileUtils
        print("✅ 工具模块导入成功")
        
        # 测试报告模块
        print("📊 测试报告模块...")
        from reports.json_reporter import JsonReporter
        from reports.markdown_reporter import MarkdownReporter
        from reports.report_converter import ReportConverter
        print("✅ 报告模块导入成功")
        
        # 测试配置模块
        print("⚙️  测试配置模块...")
        from config.settings import HSKProjectSettings, Settings
        print("✅ 配置模块导入成功")
        
        # 测试基本功能
        print("\n🔍 测试基本功能...")
        
        # 测试日期工具
        start_date, end_date = DateUtils.get_recent_date_range(7)
        print(f"📅 最近7天: {start_date} 到 {end_date}")
        
        # 测试模式匹配
        pattern_matcher = PatternMatcher()
        test_content = """
        @RestController
        @RequestMapping("/api/test")
        public class TestController {
            @GetMapping("/hello")
            public String hello() {
                return "Hello";
            }
        }
        """
        
        is_controller = pattern_matcher.is_controller_class(test_content)
        class_name = pattern_matcher.extract_class_name(test_content)
        print(f"🎯 模式匹配测试: Controller={is_controller}, 类名={class_name}")
        
        # 测试HSK项目设置
        project_info = HSKProjectSettings.get_project_info()
        print(f"🏠 HSK项目信息: {project_info}")
        
        print("\n✅ 所有模块测试通过!")
        print("\n📋 新的模块化结构工作正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_simple_analysis():
    """运行简单分析"""
    print("\n🚀 运行简单分析测试...")
    
    try:
        from config.settings import HSKProjectSettings
        from utils.git_utils import GitUtils
        from utils.date_utils import DateUtils
        
        # 检查HSK项目
        if not HSKProjectSettings.validate_project_path():
            print("❌ HSK项目路径无效")
            return False
        
        # 创建Git工具
        git_utils = GitUtils(HSKProjectSettings.PROJECT_PATH)
        
        # 获取最近3天的提交
        start_date, end_date = DateUtils.get_recent_date_range(3)
        commits = git_utils.get_commits_in_range(start_date, end_date)
        
        print(f"📊 最近3天找到 {len(commits)} 个提交")
        
        # 显示前3个提交
        for i, (commit_hash, commit_date) in enumerate(commits[:3]):
            print(f"  {i+1}. {commit_hash[:8]} ({commit_date[:10]})")
        
        if len(commits) > 3:
            print(f"  ... 还有 {len(commits) - 3} 个提交")
        
        print("✅ 简单分析测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 简单分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 HSK项目模块化结构测试")
    print("="*60)
    
    # 测试模块导入
    if not test_new_structure():
        return 1
    
    # 测试简单分析
    if not run_simple_analysis():
        return 1
    
    print("\n🎉 所有测试通过!")
    print("\n📋 使用建议:")
    print("1. 新的模块化结构工作正常")
    print("2. 可以使用 python scripts/run_hsk_analyzer.py 运行HSK分析器")
    print("3. 可以使用 python analyzers/quick_analyzer.py 运行快速分析器")
    print("4. 报告文件将保存在 output/ 目录下")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
