#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期计算
"""

from datetime import datetime, timedelta

def test_date_calculation():
    """测试日期计算"""
    print("🗓️ 测试日期计算")
    print("="*50)
    
    # 当前时间
    now = datetime.now()
    print(f"当前时间: {now}")
    print(f"当前日期: {now.strftime('%Y-%m-%d')}")
    
    # 最近30天
    print(f"\n📅 最近30天计算:")
    end_date = now
    start_date = end_date - timedelta(days=30)
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    print(f"开始日期: {start_str}")
    print(f"结束日期: {end_str}")
    print(f"天数差: {(end_date - start_date).days}")
    
    # 最近7天
    print(f"\n📅 最近7天计算:")
    end_date = now
    start_date = end_date - timedelta(days=7)
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    print(f"开始日期: {start_str}")
    print(f"结束日期: {end_str}")
    print(f"天数差: {(end_date - start_date).days}")

def test_git_date_format():
    """测试Git日期格式"""
    print(f"\n🔍 测试Git命令日期格式")
    print("="*50)
    
    import subprocess
    import os
    
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    
    if not os.path.exists(HSK_PROJECT_PATH):
        print("❌ 项目路径不存在")
        return
    
    # 测试不同的日期格式
    now = datetime.now()
    start_date = now - timedelta(days=3)
    
    formats = [
        ('%Y-%m-%d', '日期格式'),
        ('%Y-%m-%d %H:%M:%S', '日期时间格式'),
        ('%Y-%m-%d 00:00:00', '日期+零点'),
        ('%Y-%m-%d 23:59:59', '日期+末尾')
    ]
    
    for fmt, desc in formats:
        start_str = start_date.strftime(fmt)
        end_str = now.strftime(fmt)
        
        print(f"\n📋 {desc}:")
        print(f"  开始: {start_str}")
        print(f"  结束: {end_str}")
        
        try:
            command = [
                'git', 'log',
                f'--since={start_str}',
                f'--until={end_str}',
                '--oneline'
            ]
            
            result = subprocess.run(
                command,
                cwd=HSK_PROJECT_PATH,
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=10
            )
            
            if result.returncode == 0:
                commit_count = len([line for line in result.stdout.strip().split('\n') if line])
                print(f"  ✅ 找到 {commit_count} 个提交")
                
                # 显示前3个提交
                lines = result.stdout.strip().split('\n')
                for i, line in enumerate(lines[:3]):
                    if line:
                        print(f"    {i+1}. {line}")
            else:
                print(f"  ❌ 命令失败: {result.stderr}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

if __name__ == "__main__":
    test_date_calculation()
    test_git_date_format()
