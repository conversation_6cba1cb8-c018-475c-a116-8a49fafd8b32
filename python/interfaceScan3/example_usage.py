#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例脚本
演示如何使用GitInterfaceAnalyzer分析Java Spring项目
"""

import os
import sys
from datetime import datetime, timedelta
from git_interface_analyzer import GitInterfaceAnalyzer

def analyze_last_week():
    """分析最近一周的变化"""
    # 设置仓库路径为你的Spring项目路径
    repo_path = r"D:\code\Hsk-java"
    
    # 计算日期范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    print(f"分析仓库: {repo_path}")
    print(f"时间范围: {start_str} 到 {end_str}")
    
    # 创建分析器
    analyzer = GitInterfaceAnalyzer(repo_path)
    
    # 执行分析
    report = analyzer.analyze_period(start_str, end_str)
    
    # 保存报告
    output_file = f"analysis_report_{start_str}_to_{end_str}.json"
    analyzer.save_report(report, output_file)
    
    # 打印详细结果
    print_detailed_report(report)

def analyze_custom_period():
    """分析自定义时间段"""
    # 设置仓库路径为你的Spring项目路径
    repo_path = r"D:\code\Hsk-java"
    
    # 自定义时间范围
    start_date = "2024-07-01"  # 修改为你需要的开始日期
    end_date = "2024-08-12"    # 修改为你需要的结束日期
    
    print(f"分析仓库: {repo_path}")
    print(f"时间范围: {start_date} 到 {end_date}")
    
    # 创建分析器
    analyzer = GitInterfaceAnalyzer(repo_path)
    
    # 执行分析
    report = analyzer.analyze_period(start_date, end_date)
    
    # 保存报告
    output_file = f"analysis_report_{start_date}_to_{end_date}.json"
    analyzer.save_report(report, output_file)
    
    # 打印详细结果
    print_detailed_report(report)

def print_detailed_report(report):
    """打印详细报告"""
    summary = report['summary']
    
    print("\n" + "="*50)
    print("详细分析报告")
    print("="*50)
    
    print(f"\n📊 总体统计:")
    print(f"  接口总数: {summary['total_interfaces']}")
    print(f"  实体总数: {summary['total_entities']}")
    
    print(f"\n🆕 新增统计:")
    print(f"  新增接口: {summary['new_interfaces']}")
    print(f"  新增实体: {summary['new_entities']}")
    
    print(f"\n✏️ 修改统计:")
    print(f"  修改接口: {summary['modified_interfaces']}")
    print(f"  修改实体: {summary['modified_entities']}")
    
    print(f"\n🗑️ 删除统计:")
    print(f"  删除接口: {summary['deleted_interfaces']}")
    print(f"  删除实体: {summary['deleted_entities']}")
    
    # 打印新增接口详情
    if summary['new_interfaces'] > 0:
        print(f"\n🔗 新增接口详情:")
        for interface in report['interfaces']:
            if interface['operation'] == 'ADD':
                print(f"  {interface['method']} {interface['path']}")
                print(f"    类: {interface['class_name']}.{interface['method_name']}")
                print(f"    文件: {interface['file_path']}")
                print(f"    提交: {interface['commit_hash'][:8]} ({interface['commit_date']})")
                print()
    
    # 打印新增实体详情
    if summary['new_entities'] > 0:
        print(f"\n🗃️ 新增实体详情:")
        for entity in report['entities']:
            if entity['operation'] == 'ADD':
                print(f"  表: {entity['table_name']} (类: {entity['class_name']})")
                print(f"    字段: {', '.join(entity['fields'])}")
                print(f"    文件: {entity['file_path']}")
                print(f"    提交: {entity['commit_hash'][:8]} ({entity['commit_date']})")
                print()

def analyze_specific_commits():
    """分析特定提交范围"""
    # 设置仓库路径为你的Spring项目路径
    repo_path = r"D:\code\Hsk-java"
    
    analyzer = GitInterfaceAnalyzer(repo_path)
    
    # 获取最近的提交
    recent_commits = analyzer.get_commits_in_range("2024-08-01", "2024-08-12")
    
    print(f"最近的提交 ({len(recent_commits)} 个):")
    for commit_hash, commit_date in recent_commits[-10:]:  # 显示最近10个
        print(f"  {commit_hash[:8]} - {commit_date}")
    
    if recent_commits:
        # 分析最近的提交
        latest_commit = recent_commits[-1]
        print(f"\n分析最新提交: {latest_commit[0][:8]}")
        
        changed_files = analyzer.get_changed_files(latest_commit[0])
        print(f"变更的Java文件 ({len(changed_files)} 个):")
        for status, file_path in changed_files:
            print(f"  {status} {file_path}")

def main():
    """主函数"""
    print("Git接口和实体分析工具 - 使用示例")
    print("="*50)
    
    while True:
        print("\n请选择分析模式:")
        print("1. 分析最近一周")
        print("2. 分析自定义时间段")
        print("3. 查看最近提交")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        try:
            if choice == "1":
                analyze_last_week()
            elif choice == "2":
                analyze_custom_period()
            elif choice == "3":
                analyze_specific_commits()
            elif choice == "4":
                print("退出程序")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"执行过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
