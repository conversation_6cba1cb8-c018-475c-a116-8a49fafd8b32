{"summary": {"total_interfaces": 915, "total_entities": 80, "new_interfaces": 9, "new_entities": 10, "modified_interfaces": 906, "modified_entities": 70, "deleted_interfaces": 0, "deleted_entities": 0}, "interfaces": [{"method": "POST", "path": "/edu/interactive-course/v1/page", "class_name": "InteractiveCourseAppController", "method_name": "getInteractiveCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseAppController.java", "commit_hash": "2e3759fd950cfdd5f2589772cb4f8dbea4d43b36", "commit_date": "2025-07-14 13:32:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/v1/get", "class_name": "InteractiveCourseAppController", "method_name": "getInteractiveCourseDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseAppController.java", "commit_hash": "2e3759fd950cfdd5f2589772cb4f8dbea4d43b36", "commit_date": "2025-07-14 13:32:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/v1/get-progress", "class_name": "InteractiveCourseAppController", "method_name": "getInteractiveCourseProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseAppController.java", "commit_hash": "2e3759fd950cfdd5f2589772cb4f8dbea4d43b36", "commit_date": "2025-07-14 13:32:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/page", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "2e3759fd950cfdd5f2589772cb4f8dbea4d43b36", "commit_date": "2025-07-14 13:32:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/get", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "2e3759fd950cfdd5f2589772cb4f8dbea4d43b36", "commit_date": "2025-07-14 13:32:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/videoRecord", "class_name": "InteractiveCourseUnitAppController", "method_name": "recordVideoWatch", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "2e3759fd950cfdd5f2589772cb4f8dbea4d43b36", "commit_date": "2025-07-14 13:32:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/submit-answer", "class_name": "GameAnswerRecordAppController", "method_name": "submitAnswer", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "e5873bc56f3611ff5f6ae2fa69e2370479519bc8", "commit_date": "2025-07-14 15:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/answer-report", "class_name": "GameAnswerRecordAppController", "method_name": "getAnswerReport", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "e5873bc56f3611ff5f6ae2fa69e2370479519bc8", "commit_date": "2025-07-14 15:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/v1/page", "class_name": "InteractiveCourseAppController", "method_name": "getInteractiveCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseAppController.java", "commit_hash": "7e3c3609a74bb46363e895bcf53aff0055c39ad3", "commit_date": "2025-07-14 16:01:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/v1/get", "class_name": "InteractiveCourseAppController", "method_name": "getInteractiveCourseDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseAppController.java", "commit_hash": "7e3c3609a74bb46363e895bcf53aff0055c39ad3", "commit_date": "2025-07-14 16:01:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/v1/get-progress", "class_name": "InteractiveCourseAppController", "method_name": "getInteractiveCourseProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseAppController.java", "commit_hash": "7e3c3609a74bb46363e895bcf53aff0055c39ad3", "commit_date": "2025-07-14 16:01:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/trade/order/page", "class_name": "OrderAdminController", "method_name": "getInteractiveCoursePage", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/controller/admin/order/OrderAdminController.java", "commit_hash": "7e3c3609a74bb46363e895bcf53aff0055c39ad3", "commit_date": "2025-07-14 16:01:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/trade/order/export", "class_name": "OrderAdminController", "method_name": "exportInteractiveCourse", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/controller/admin/order/OrderAdminController.java", "commit_hash": "7e3c3609a74bb46363e895bcf53aff0055c39ad3", "commit_date": "2025-07-14 16:01:02 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/trade/order/getOrderDetail", "class_name": "OrderAdminController", "method_name": "getInteractiveCourse", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/controller/admin/order/OrderAdminController.java", "commit_hash": "7e3c3609a74bb46363e895bcf53aff0055c39ad3", "commit_date": "2025-07-14 16:01:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/page", "class_name": "SpecialExerciseAppController", "method_name": "getSpecialExercisePage", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "f8c6305cdc21bf6ee09f8e33635e9e66c7dccf01", "commit_date": "2025-07-14 19:15:30 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/get", "class_name": "SpecialExerciseAppController", "method_name": "getSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "f8c6305cdc21bf6ee09f8e33635e9e66c7dccf01", "commit_date": "2025-07-14 19:15:30 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/practice-count", "class_name": "SpecialExerciseAppController", "method_name": "getSpecialExercisePracticeCount", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "f8c6305cdc21bf6ee09f8e33635e9e66c7dccf01", "commit_date": "2025-07-14 19:15:30 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/start", "class_name": "SpecialExerciseAppController", "method_name": "startSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "f8c6305cdc21bf6ee09f8e33635e9e66c7dccf01", "commit_date": "2025-07-14 19:15:30 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/report", "class_name": "SpecialExerciseAppController", "method_name": "report", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "f8c6305cdc21bf6ee09f8e33635e9e66c7dccf01", "commit_date": "2025-07-14 19:15:30 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-detail/stats", "class_name": "ExamDetailAdminController", "method_name": "getExamStats", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamDetailAdminController.java", "commit_hash": "fd3283da6e09ce8434dfe9ebb30fc88f8ac7b93a", "commit_date": "2025-07-14 19:31:21 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/thirdparty/writing-ai-correction-detail/create", "class_name": "WritingAiCorrectionDetailController", "method_name": "createWritingAiCorrectionDetail", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/writingaicorrectiondetail/WritingAiCorrectionDetailController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/thirdparty/writing-ai-correction-detail/update", "class_name": "WritingAiCorrectionDetailController", "method_name": "updateWritingAiCorrectionDetail", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/writingaicorrectiondetail/WritingAiCorrectionDetailController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/thirdparty/writing-ai-correction-detail/delete", "class_name": "WritingAiCorrectionDetailController", "method_name": "deleteWritingAiCorrectionDetail", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/writingaicorrectiondetail/WritingAiCorrectionDetailController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/thirdparty/writing-ai-correction-detail/get", "class_name": "WritingAiCorrectionDetailController", "method_name": "getWritingAiCorrectionDetail", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/writingaicorrectiondetail/WritingAiCorrectionDetailController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/thirdparty/writing-ai-correction-record/create", "class_name": "WritingAiCorrectionRecordController", "method_name": "createWritingAiCorrectionRecord", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/writingaicorrectionrecord/WritingAiCorrectionRecordController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/thirdparty/writing-ai-correction-record/update", "class_name": "WritingAiCorrectionRecordController", "method_name": "updateWritingAiCorrectionRecord", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/writingaicorrectionrecord/WritingAiCorrectionRecordController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/thirdparty/writing-ai-correction-record/delete", "class_name": "WritingAiCorrectionRecordController", "method_name": "deleteWritingAiCorrectionRecord", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/writingaicorrectionrecord/WritingAiCorrectionRecordController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/thirdparty/writing-ai-correction-record/get", "class_name": "WritingAiCorrectionRecordController", "method_name": "getWritingAiCorrectionRecord", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/writingaicorrectionrecord/WritingAiCorrectionRecordController.java", "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/home/<USER>", "class_name": "HomeController", "method_name": "getHomeInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/home/<USER>", "commit_hash": "790da3066cb8e18c0d2c07cc313b2156bf67424d", "commit_date": "2025-07-15 19:34:17 +0800", "operation": "ADD"}, {"method": "POST", "path": "/game/special-exercise/v1/page", "class_name": "SpecialExerciseAppController", "method_name": "getSpecialExercisePage", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "290c282189c3383b0cb2b961034250a81ff41bae", "commit_date": "2025-07-16 13:11:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/get", "class_name": "SpecialExerciseAppController", "method_name": "getSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "290c282189c3383b0cb2b961034250a81ff41bae", "commit_date": "2025-07-16 13:11:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/practice-count", "class_name": "SpecialExerciseAppController", "method_name": "getSpecialExercisePracticeCount", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "290c282189c3383b0cb2b961034250a81ff41bae", "commit_date": "2025-07-16 13:11:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/start", "class_name": "SpecialExerciseAppController", "method_name": "startSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "290c282189c3383b0cb2b961034250a81ff41bae", "commit_date": "2025-07-16 13:11:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/v1/report", "class_name": "SpecialExerciseAppController", "method_name": "report", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/specialexercise/SpecialExerciseAppController.java", "commit_hash": "290c282189c3383b0cb2b961034250a81ff41bae", "commit_date": "2025-07-16 13:11:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "3a21e3c465c3523553a54e05a1c4fa0d78373813", "commit_date": "2025-07-16 16:29:35 +0800", "operation": "ADD"}, {"method": "POST", "path": "/user/sms/sendLoginSmsCode", "class_name": "SmsController", "method_name": "sendLoginSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "db12894ac239b5d6e3069e9f1fc180d97a0352d3", "commit_date": "2025-07-16 16:48:44 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendResetPasswordSmsCode", "class_name": "SmsController", "method_name": "sendResetPasswordSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "db12894ac239b5d6e3069e9f1fc180d97a0352d3", "commit_date": "2025-07-16 16:48:44 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/page", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "8735e70684f36b30b5be6e7425264aa02a3c9c39", "commit_date": "2025-07-16 18:11:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/get", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "8735e70684f36b30b5be6e7425264aa02a3c9c39", "commit_date": "2025-07-16 18:11:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/videoRecord", "class_name": "InteractiveCourseUnitAppController", "method_name": "recordVideoWatch", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "8735e70684f36b30b5be6e7425264aa02a3c9c39", "commit_date": "2025-07-16 18:11:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/getNextUnitInfo", "class_name": "InteractiveCourseUnitAppController", "method_name": "getNextUnitInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "8735e70684f36b30b5be6e7425264aa02a3c9c39", "commit_date": "2025-07-16 18:11:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "6d76dda4be78499fdf3f8d91144a607e66b264d9", "commit_date": "2025-07-17 11:29:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "6d76dda4be78499fdf3f8d91144a607e66b264d9", "commit_date": "2025-07-17 11:29:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "a6016e3a4cdb3b4dcc7e404ca3106e0282ca0d01", "commit_date": "2025-07-17 17:02:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/page", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "67ef5dcf701faa20b432447d9a3bc9403e453987", "commit_date": "2025-07-17 18:57:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/get", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "67ef5dcf701faa20b432447d9a3bc9403e453987", "commit_date": "2025-07-17 18:57:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/videoRecord", "class_name": "InteractiveCourseUnitAppController", "method_name": "recordVideoWatch", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "67ef5dcf701faa20b432447d9a3bc9403e453987", "commit_date": "2025-07-17 18:57:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/getNextUnitInfo", "class_name": "InteractiveCourseUnitAppController", "method_name": "getNextUnitInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "67ef5dcf701faa20b432447d9a3bc9403e453987", "commit_date": "2025-07-17 18:57:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/updateRecordSaveOption", "class_name": "InteractiveCourseUnitAppController", "method_name": "updateRecordSaveOption", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "67ef5dcf701faa20b432447d9a3bc9403e453987", "commit_date": "2025-07-17 18:57:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "71e35fb454344d63c3a033237a267b3e20fd0693", "commit_date": "2025-07-17 19:45:26 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "71e35fb454344d63c3a033237a267b3e20fd0693", "commit_date": "2025-07-17 19:45:26 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "71e35fb454344d63c3a033237a267b3e20fd0693", "commit_date": "2025-07-17 19:45:26 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/home/<USER>", "class_name": "HomeController", "method_name": "getHomeInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/home/<USER>", "commit_hash": "c526d5e0a9e31b6488fa122fe78fdbcc6bf20db5", "commit_date": "2025-07-18 13:15:34 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/home/<USER>", "class_name": "HomeController", "method_name": "getHomeInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/home/<USER>", "commit_hash": "6833cb25f542163d07c6fcc03f0121287119b820", "commit_date": "2025-07-18 13:38:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/page", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "6833cb25f542163d07c6fcc03f0121287119b820", "commit_date": "2025-07-18 13:38:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/get", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "6833cb25f542163d07c6fcc03f0121287119b820", "commit_date": "2025-07-18 13:38:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/videoRecord", "class_name": "InteractiveCourseUnitAppController", "method_name": "recordVideoWatch", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "6833cb25f542163d07c6fcc03f0121287119b820", "commit_date": "2025-07-18 13:38:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/getNextUnitInfo", "class_name": "InteractiveCourseUnitAppController", "method_name": "getNextUnitInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "6833cb25f542163d07c6fcc03f0121287119b820", "commit_date": "2025-07-18 13:38:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/updateRecordSaveOption", "class_name": "InteractiveCourseUnitAppController", "method_name": "updateRecordSaveOption", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "6833cb25f542163d07c6fcc03f0121287119b820", "commit_date": "2025-07-18 13:38:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "2c0defb5c291fd9ad60d9f7a286b54bf3f3cc178", "commit_date": "2025-07-18 16:20:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/create", "class_name": "InteractiveCourseController", "method_name": "createInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course/update", "class_name": "InteractiveCourseController", "method_name": "updateInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/interactive-course/delete", "class_name": "InteractiveCourseController", "method_name": "deleteInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/interactive-course/get", "class_name": "InteractiveCourseController", "method_name": "getInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/page", "class_name": "InteractiveCourseController", "method_name": "getInteractiveCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course/update-status", "class_name": "InteractiveCourseController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course/update-sort", "class_name": "InteractiveCourseController", "method_name": "updateSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/export", "class_name": "InteractiveCourseController", "method_name": "exportInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/create", "class_name": "InteractiveCourseUnitController", "method_name": "createInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/interactive-course-unit/delete", "class_name": "InteractiveCourseUnitController", "method_name": "deleteInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/interactive-course-unit/get/v2", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitV2", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/page", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-status/{id}", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-sort", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/export", "class_name": "InteractiveCourseUnitController", "method_name": "exportInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "745463fc6a37e9d1fb1dea7634240b2b7ed7cdc5", "commit_date": "2025-07-18 16:24:47 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/create", "class_name": "InteractiveCourseUnitController", "method_name": "createInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/interactive-course-unit/delete", "class_name": "InteractiveCourseUnitController", "method_name": "deleteInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/interactive-course-unit/get/v2", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitV2", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/page", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-status/{id}", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-sort", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/export", "class_name": "InteractiveCourseUnitController", "method_name": "exportInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/teacher/create", "class_name": "TeacherController", "method_name": "create<PERSON><PERSON>er", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/teacher/update", "class_name": "TeacherController", "method_name": "update<PERSON><PERSON>er", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/teacher/delete", "class_name": "TeacherController", "method_name": "deleteTeacher", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/teacher/get", "class_name": "TeacherController", "method_name": "<PERSON><PERSON><PERSON>er", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/teacher/page", "class_name": "TeacherController", "method_name": "getTeacherPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/teacher/update-status", "class_name": "TeacherController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/teacher/update-sort", "class_name": "TeacherController", "method_name": "updateSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/teacher/export", "class_name": "TeacherController", "method_name": "exportTeacher", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/teacher/export-course", "class_name": "TeacherController", "method_name": "exportTeacherCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/create", "class_name": "TextbookController", "method_name": "createTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/update", "class_name": "TextbookController", "method_name": "updateTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/delete", "class_name": "TextbookController", "method_name": "deleteTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/get", "class_name": "TextbookController", "method_name": "getTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/page", "class_name": "TextbookController", "method_name": "getTextbookPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/pageChapter", "class_name": "TextbookController", "method_name": "getChapterPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getChapter", "class_name": "TextbookController", "method_name": "get<PERSON><PERSON>pter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/saveChapter", "class_name": "TextbookController", "method_name": "saveChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/deleteChapter", "class_name": "TextbookController", "method_name": "deleteChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getChapterHasQuestion", "class_name": "TextbookController", "method_name": "getChapterHasQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/enableOrDisableChapter", "class_name": "TextbookController", "method_name": "enableOrDisableChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/deleteUnit", "class_name": "TextbookController", "method_name": "deleteUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getUnitHasQuestion", "class_name": "TextbookController", "method_name": "getUnitHasQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/treeList", "class_name": "TextbookController", "method_name": "tree", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/mockTextbook", "class_name": "TextbookController", "method_name": "mockTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "22d52009476de33afb27b8f98d3b83fdcbacb7cf", "commit_date": "2025-07-18 17:04:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course-category/create", "class_name": "EliteCourseCategoryController", "method_name": "createCourseCategory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course-category/update", "class_name": "EliteCourseCategoryController", "method_name": "updateCourseCategory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/elite-course-category/delete", "class_name": "EliteCourseCategoryController", "method_name": "deleteCourseCategory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course-category/check-delete", "class_name": "EliteCourseCategoryController", "method_name": "checkCourseCategoryCanDelete", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course-category/get", "class_name": "EliteCourseCategoryController", "method_name": "getCourseCategory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course-category/page", "class_name": "EliteCourseCategoryController", "method_name": "getCourseCategoryPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course-category/sort", "class_name": "EliteCourseCategoryController", "method_name": "updateCourseCategorySort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course-category/list", "class_name": "EliteCourseCategoryController", "method_name": "getAll", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course-category/list-by-hsk", "class_name": "EliteCourseCategoryController", "method_name": "getByHskLevel", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/create", "class_name": "EliteCourseController", "method_name": "createEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course/update", "class_name": "EliteCourseController", "method_name": "updateEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/elite-course/delete", "class_name": "EliteCourseController", "method_name": "deleteEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/check-delete", "class_name": "EliteCourseController", "method_name": "checkCourseCanDelete", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/get", "class_name": "EliteCourseController", "method_name": "getEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/page", "class_name": "EliteCourseController", "method_name": "getEliteCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/directory", "class_name": "EliteCourseController", "method_name": "getCourseDirectory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course/update-status", "class_name": "EliteCourseController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course/update-listing-status", "class_name": "EliteCourseController", "method_name": "updateListingStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course/sort", "class_name": "EliteCourseController", "method_name": "updateSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/export", "class_name": "EliteCourseController", "method_name": "exportEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/overview", "class_name": "EliteCourseController", "method_name": "getCourseOverview", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/create", "class_name": "InteractiveCourseUnitController", "method_name": "createInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/interactive-course-unit/delete", "class_name": "InteractiveCourseUnitController", "method_name": "deleteInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/interactive-course-unit/get/v2", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitV2", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/page", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-status/{id}", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-sort", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/export", "class_name": "InteractiveCourseUnitController", "method_name": "exportInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/create", "class_name": "TextbookController", "method_name": "createTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/update", "class_name": "TextbookController", "method_name": "updateTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/delete", "class_name": "TextbookController", "method_name": "deleteTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/get", "class_name": "TextbookController", "method_name": "getTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/page", "class_name": "TextbookController", "method_name": "getTextbookPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/pageChapter", "class_name": "TextbookController", "method_name": "getChapterPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getChapter", "class_name": "TextbookController", "method_name": "get<PERSON><PERSON>pter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/saveChapter", "class_name": "TextbookController", "method_name": "saveChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/deleteChapter", "class_name": "TextbookController", "method_name": "deleteChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getChapterHasQuestion", "class_name": "TextbookController", "method_name": "getChapterHasQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/enableOrDisableChapter", "class_name": "TextbookController", "method_name": "enableOrDisableChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/deleteUnit", "class_name": "TextbookController", "method_name": "deleteUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getUnitHasQuestion", "class_name": "TextbookController", "method_name": "getUnitHasQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/treeList", "class_name": "TextbookController", "method_name": "tree", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/mockTextbook", "class_name": "TextbookController", "method_name": "mockTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/trade/order/page", "class_name": "OrderAdminController", "method_name": "getInteractiveCoursePage", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/controller/admin/order/OrderAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/trade/order/export", "class_name": "OrderAdminController", "method_name": "exportInteractiveCourse", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/controller/admin/order/OrderAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/trade/order/getOrderDetail", "class_name": "OrderAdminController", "method_name": "getInteractiveCourse", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/controller/admin/order/OrderAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/userOrderItemList", "class_name": "AppUserAdminController", "method_name": "userOrderItemList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/create", "class_name": "AppUserAdminController", "method_name": "createUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/user/update", "class_name": "AppUserAdminController", "method_name": "updateUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/user/delete", "class_name": "AppUserAdminController", "method_name": "deleteUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/user/update-status", "class_name": "AppUserAdminController", "method_name": "updateUserStatus", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/user/reset-password", "class_name": "AppUserAdminController", "method_name": "resetPassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/page", "class_name": "AppUserAdminController", "method_name": "getInteractiveCoursePage", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/get", "class_name": "AppUserAdminController", "method_name": "getUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/export", "class_name": "AppUserAdminController", "method_name": "exportUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/import/template", "class_name": "AppUserAdminController", "method_name": "downloadImportTemplate", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/import", "class_name": "AppUserAdminController", "method_name": "importUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e", "commit_date": "2025-07-18 18:33:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/create", "class_name": "InteractiveCourseController", "method_name": "createInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "d30774124b2974272329a0e675d76d6da142b04c", "commit_date": "2025-07-19 09:47:12 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course/update", "class_name": "InteractiveCourseController", "method_name": "updateInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "d30774124b2974272329a0e675d76d6da142b04c", "commit_date": "2025-07-19 09:47:12 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/interactive-course/delete", "class_name": "InteractiveCourseController", "method_name": "deleteInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "d30774124b2974272329a0e675d76d6da142b04c", "commit_date": "2025-07-19 09:47:12 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/interactive-course/get", "class_name": "InteractiveCourseController", "method_name": "getInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "d30774124b2974272329a0e675d76d6da142b04c", "commit_date": "2025-07-19 09:47:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/page", "class_name": "InteractiveCourseController", "method_name": "getInteractiveCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "d30774124b2974272329a0e675d76d6da142b04c", "commit_date": "2025-07-19 09:47:12 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course/update-status", "class_name": "InteractiveCourseController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "d30774124b2974272329a0e675d76d6da142b04c", "commit_date": "2025-07-19 09:47:12 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course/update-sort", "class_name": "InteractiveCourseController", "method_name": "updateSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "d30774124b2974272329a0e675d76d6da142b04c", "commit_date": "2025-07-19 09:47:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course/export", "class_name": "InteractiveCourseController", "method_name": "exportInteractiveCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseController.java", "commit_hash": "d30774124b2974272329a0e675d76d6da142b04c", "commit_date": "2025-07-19 09:47:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/create", "class_name": "QuestionController", "method_name": "createQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question/update", "class_name": "QuestionController", "method_name": "updateQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/updateStatus", "class_name": "QuestionController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/get", "class_name": "QuestionController", "method_name": "getQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getByQuestionId", "class_name": "QuestionController", "method_name": "getByQuestionId", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/page", "class_name": "QuestionController", "method_name": "getQuestionPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/import/template", "class_name": "QuestionController", "method_name": "downloadImportTemplate", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/executeData", "class_name": "QuestionController", "method_name": "executeData", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/import", "class_name": "QuestionController", "method_name": "importQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/export", "class_name": "QuestionController", "method_name": "export", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/testXunFeiUpload", "class_name": "QuestionController", "method_name": "testXunFeiUpload", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/testXunFeiCallback", "class_name": "QuestionController", "method_name": "testXunFeiCallback", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/testXunFeiGetResult", "class_name": "QuestionController", "method_name": "testXunFeiGetResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/create", "class_name": "WordController", "method_name": "createWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/word/update", "class_name": "WordController", "method_name": "updateWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/delete", "class_name": "WordController", "method_name": "deleteWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/get", "class_name": "WordController", "method_name": "getWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/page", "class_name": "WordController", "method_name": "getWordPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/getQuoteInfo", "class_name": "WordController", "method_name": "getQuoteInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/export", "class_name": "WordController", "method_name": "exportWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/getSpecialExerciseQuote", "class_name": "WordController", "method_name": "getQuestionInfoListByWordId", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/getInteractiveCourseQuote", "class_name": "WordController", "method_name": "getInteractiveCourseQuoteByWordId", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/getSpecialExerciseQuoteInfo", "class_name": "WordController", "method_name": "getSpecialExerciseQuoteInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "85fc371d504c0cf36a323849f6ec10c5e7308954", "commit_date": "2025-07-19 11:19:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0e3acf588c008c4e0ef89fabfc7a5956cec0bcc1", "commit_date": "2025-07-20 17:17:09 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0e3acf588c008c4e0ef89fabfc7a5956cec0bcc1", "commit_date": "2025-07-20 17:17:09 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0e3acf588c008c4e0ef89fabfc7a5956cec0bcc1", "commit_date": "2025-07-20 17:17:09 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0e3acf588c008c4e0ef89fabfc7a5956cec0bcc1", "commit_date": "2025-07-20 17:17:09 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "d88783c214eff51d33f1b56493088bf95f0a6dd3", "commit_date": "2025-07-20 23:02:51 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "d88783c214eff51d33f1b56493088bf95f0a6dd3", "commit_date": "2025-07-20 23:02:51 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "d88783c214eff51d33f1b56493088bf95f0a6dd3", "commit_date": "2025-07-20 23:02:51 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "d88783c214eff51d33f1b56493088bf95f0a6dd3", "commit_date": "2025-07-20 23:02:51 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "d88783c214eff51d33f1b56493088bf95f0a6dd3", "commit_date": "2025-07-20 23:02:51 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b6eae7682ee72071ad8e5197587bce75ac5bd646", "commit_date": "2025-07-21 00:24:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b6eae7682ee72071ad8e5197587bce75ac5bd646", "commit_date": "2025-07-21 00:24:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b6eae7682ee72071ad8e5197587bce75ac5bd646", "commit_date": "2025-07-21 00:24:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b6eae7682ee72071ad8e5197587bce75ac5bd646", "commit_date": "2025-07-21 00:24:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b6eae7682ee72071ad8e5197587bce75ac5bd646", "commit_date": "2025-07-21 00:24:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b6eae7682ee72071ad8e5197587bce75ac5bd646", "commit_date": "2025-07-21 00:24:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "4eda75d61ceabf68d718d7db165fd94a6547f9c6", "commit_date": "2025-07-21 13:34:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "4eda75d61ceabf68d718d7db165fd94a6547f9c6", "commit_date": "2025-07-21 13:34:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "4eda75d61ceabf68d718d7db165fd94a6547f9c6", "commit_date": "2025-07-21 13:34:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "4eda75d61ceabf68d718d7db165fd94a6547f9c6", "commit_date": "2025-07-21 13:34:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "4eda75d61ceabf68d718d7db165fd94a6547f9c6", "commit_date": "2025-07-21 13:34:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "4eda75d61ceabf68d718d7db165fd94a6547f9c6", "commit_date": "2025-07-21 13:34:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "4eda75d61ceabf68d718d7db165fd94a6547f9c6", "commit_date": "2025-07-21 13:34:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail/create", "class_name": "QuestionDetailController", "method_name": "createQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-detail/update", "class_name": "QuestionDetailController", "method_name": "updateQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail/delete", "class_name": "QuestionDetailController", "method_name": "deleteQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/create", "class_name": "QuestionDetailVersionController", "method_name": "createQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-detail-version/update", "class_name": "QuestionDetailVersionController", "method_name": "updateQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/delete", "class_name": "QuestionDetailVersionController", "method_name": "deleteQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/get", "class_name": "QuestionDetailVersionController", "method_name": "getQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-type/create", "class_name": "QuestionTypeController", "method_name": "createQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiontype/QuestionTypeController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-type/update", "class_name": "QuestionTypeController", "method_name": "updateQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiontype/QuestionTypeController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-type/delete", "class_name": "QuestionTypeController", "method_name": "deleteQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiontype/QuestionTypeController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-type/get", "class_name": "QuestionTypeController", "method_name": "getQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiontype/QuestionTypeController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-type/list", "class_name": "QuestionTypeController", "method_name": "getQuestionTypePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiontype/QuestionTypeController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-type/getAll", "class_name": "QuestionTypeController", "method_name": "getAllQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiontype/QuestionTypeController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-type/tree", "class_name": "QuestionTypeController", "method_name": "getQuestionTypeTree", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiontype/QuestionTypeController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/create", "class_name": "QuestionVersionController", "method_name": "createQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-version/update", "class_name": "QuestionVersionController", "method_name": "updateQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/delete", "class_name": "QuestionVersionController", "method_name": "deleteQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/get", "class_name": "QuestionVersionController", "method_name": "getQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-question-answer-data/create", "class_name": "UserQuestionAnswerDataController", "method_name": "createUserQuestionAnswerData", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/userquestionanswerdata/UserQuestionAnswerDataController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/user-question-answer-data/update", "class_name": "UserQuestionAnswerDataController", "method_name": "updateUserQuestionAnswerData", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/userquestionanswerdata/UserQuestionAnswerDataController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-question-answer-data/delete", "class_name": "UserQuestionAnswerDataController", "method_name": "deleteUserQuestionAnswerData", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/userquestionanswerdata/UserQuestionAnswerDataController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-question-answer-data/get", "class_name": "UserQuestionAnswerDataController", "method_name": "getUserQuestionAnswerData", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/userquestionanswerdata/UserQuestionAnswerDataController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/info", "class_name": "AppUserCenterController", "method_name": "getUserCenterInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "39c8f65541ad1cfb09f2609ee048c9751631d7f9", "commit_date": "2025-07-21 13:43:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//getUserInfo", "class_name": "AppUserController", "method_name": "getUserInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "1c13e87493ff173810339cb12bd3bdf3007776a7", "commit_date": "2025-07-21 17:13:58 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//logout", "class_name": "AppUserController", "method_name": "logout", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "1c13e87493ff173810339cb12bd3bdf3007776a7", "commit_date": "2025-07-21 17:13:58 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//setPassword", "class_name": "AppUserController", "method_name": "setPassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "1c13e87493ff173810339cb12bd3bdf3007776a7", "commit_date": "2025-07-21 17:13:58 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//setUserInfo", "class_name": "AppUserController", "method_name": "setUserInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "1c13e87493ff173810339cb12bd3bdf3007776a7", "commit_date": "2025-07-21 17:13:58 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//updateUserArea", "class_name": "AppUserController", "method_name": "updateUserArea", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "1c13e87493ff173810339cb12bd3bdf3007776a7", "commit_date": "2025-07-21 17:13:58 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//updatePassword", "class_name": "AppUserController", "method_name": "updatePassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "1c13e87493ff173810339cb12bd3bdf3007776a7", "commit_date": "2025-07-21 17:13:58 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/correction/v1/remaining-count", "class_name": "GameCorrectionAppController", "method_name": "getUserRemainingCount", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/correction/GameCorrectionAppController.java", "commit_hash": "c9d82987446ba39719d07117179163714b70070e", "commit_date": "2025-07-21 17:51:03 +0800", "operation": "ADD"}, {"method": "POST", "path": "/game/record/submit-answer", "class_name": "GameAnswerRecordAppController", "method_name": "submitAnswer", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "c9d82987446ba39719d07117179163714b70070e", "commit_date": "2025-07-21 17:51:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/answer-report", "class_name": "GameAnswerRecordAppController", "method_name": "getAnswerReport", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "c9d82987446ba39719d07117179163714b70070e", "commit_date": "2025-07-21 17:51:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/v1/correction-remaining-count", "class_name": "GameAnswerRecordAppController", "method_name": "getUserRemainingCount", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "c9d82987446ba39719d07117179163714b70070e", "commit_date": "2025-07-21 17:51:03 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/submit-answer", "class_name": "GameAnswerRecordAppController", "method_name": "submitAnswer", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "0c59edce18d2976a138340374ac4dcb30b3cf021", "commit_date": "2025-07-21 18:00:23 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/answer-report", "class_name": "GameAnswerRecordAppController", "method_name": "getAnswerReport", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "0c59edce18d2976a138340374ac4dcb30b3cf021", "commit_date": "2025-07-21 18:00:23 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendLoginSmsCode", "class_name": "SmsController", "method_name": "sendLoginSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendResetPasswordSmsCode", "class_name": "SmsController", "method_name": "sendResetPasswordSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendChangeMobileCode", "class_name": "SmsController", "method_name": "sendChangeMobileCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//getUserInfo", "class_name": "AppUserController", "method_name": "getUserInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//logout", "class_name": "AppUserController", "method_name": "logout", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//setPassword", "class_name": "AppUserController", "method_name": "setPassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//setUserInfo", "class_name": "AppUserController", "method_name": "setUserInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//updateUserArea", "class_name": "AppUserController", "method_name": "updateUserArea", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//updatePassword", "class_name": "AppUserController", "method_name": "updatePassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//changeMobile", "class_name": "AppUserController", "method_name": "changeMobile", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "f0b26a12f85101420395a037e4abb422b8e6039a", "commit_date": "2025-07-22 11:06:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/info", "class_name": "AppUserCenterController", "method_name": "getUserCenterInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "d88b223eae123faaf320ad3b1e954ff753173550", "commit_date": "2025-07-22 14:50:58 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/recordUserBehavior", "class_name": "AppUserCenterController", "method_name": "recordUser<PERSON>eh<PERSON>or", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "d88b223eae123faaf320ad3b1e954ff753173550", "commit_date": "2025-07-22 14:50:58 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/page", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "55f2b154f452385a94f0a1fd4eb0be1ead70bca7", "commit_date": "2025-07-22 15:56:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/get", "class_name": "InteractiveCourseUnitAppController", "method_name": "getInteractiveCourseUnitDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "55f2b154f452385a94f0a1fd4eb0be1ead70bca7", "commit_date": "2025-07-22 15:56:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/videoRecord", "class_name": "InteractiveCourseUnitAppController", "method_name": "recordVideoWatch", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "55f2b154f452385a94f0a1fd4eb0be1ead70bca7", "commit_date": "2025-07-22 15:56:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/getNextUnitInfo", "class_name": "InteractiveCourseUnitAppController", "method_name": "getNextUnitInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "55f2b154f452385a94f0a1fd4eb0be1ead70bca7", "commit_date": "2025-07-22 15:56:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/getCurrentUnitUnitInfo", "class_name": "InteractiveCourseUnitAppController", "method_name": "getQuestionInfoList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "55f2b154f452385a94f0a1fd4eb0be1ead70bca7", "commit_date": "2025-07-22 15:56:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/v1/updateRecordSaveOption", "class_name": "InteractiveCourseUnitAppController", "method_name": "updateRecordSaveOption", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/interactivecourse/InteractiveCourseUnitAppController.java", "commit_hash": "55f2b154f452385a94f0a1fd4eb0be1ead70bca7", "commit_date": "2025-07-22 15:56:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//getUserInfo", "class_name": "AppUserController", "method_name": "getUserInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "193a11ba1ffb10174f8119a957f3efb7819a60bc", "commit_date": "2025-07-23 10:31:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//logout", "class_name": "AppUserController", "method_name": "logout", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "193a11ba1ffb10174f8119a957f3efb7819a60bc", "commit_date": "2025-07-23 10:31:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//setPassword", "class_name": "AppUserController", "method_name": "setPassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "193a11ba1ffb10174f8119a957f3efb7819a60bc", "commit_date": "2025-07-23 10:31:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//logoutUser", "class_name": "AppUserController", "method_name": "logoutUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "193a11ba1ffb10174f8119a957f3efb7819a60bc", "commit_date": "2025-07-23 10:31:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//setUserInfo", "class_name": "AppUserController", "method_name": "setUserInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "193a11ba1ffb10174f8119a957f3efb7819a60bc", "commit_date": "2025-07-23 10:31:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//updateUserArea", "class_name": "AppUserController", "method_name": "updateUserArea", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "193a11ba1ffb10174f8119a957f3efb7819a60bc", "commit_date": "2025-07-23 10:31:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//updatePassword", "class_name": "AppUserController", "method_name": "updatePassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "193a11ba1ffb10174f8119a957f3efb7819a60bc", "commit_date": "2025-07-23 10:31:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//changeMobile", "class_name": "AppUserController", "method_name": "changeMobile", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "193a11ba1ffb10174f8119a957f3efb7819a60bc", "commit_date": "2025-07-23 10:31:13 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/info", "class_name": "AppUserCenterController", "method_name": "getUserCenterInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "7fdb2f74d5b744778651dcbbfca269d3bea9c6ed", "commit_date": "2025-07-23 11:33:26 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/recordUserBehavior", "class_name": "AppUserCenterController", "method_name": "recordUser<PERSON>eh<PERSON>or", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "7fdb2f74d5b744778651dcbbfca269d3bea9c6ed", "commit_date": "2025-07-23 11:33:26 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/gameQuestionsList", "class_name": "UserFavoriteAppController", "method_name": "gameQuestionsList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "cc118c598fe463f7fc882cb8de51f526565022ff", "commit_date": "2025-07-23 15:27:24 +0800", "operation": "ADD"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "ad22b2edca0bda5005b6b19e7bacfc900265b26f", "commit_date": "2025-07-23 16:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/gameQuestionsList", "class_name": "UserFavoriteAppController", "method_name": "gameQuestionsList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "14a24b21c583d74e3c6b7913e0b8890b313660b5", "commit_date": "2025-07-23 16:48:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail/create", "class_name": "QuestionDetailController", "method_name": "createQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-detail/update", "class_name": "QuestionDetailController", "method_name": "updateQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail/delete", "class_name": "QuestionDetailController", "method_name": "deleteQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/create", "class_name": "QuestionDetailVersionController", "method_name": "createQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-detail-version/update", "class_name": "QuestionDetailVersionController", "method_name": "updateQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/delete", "class_name": "QuestionDetailVersionController", "method_name": "deleteQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/get", "class_name": "QuestionDetailVersionController", "method_name": "getQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/create", "class_name": "QuestionVersionController", "method_name": "createQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-version/update", "class_name": "QuestionVersionController", "method_name": "updateQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/delete", "class_name": "QuestionVersionController", "method_name": "deleteQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/get", "class_name": "QuestionVersionController", "method_name": "getQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "0c76f2818a57c2d014598c5c475ef4936902803f", "commit_date": "2025-07-23 16:59:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/favorite/word/status", "class_name": "GameFavoriteAppController", "method_name": "checkWordFavoriteStatus", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/favorite/GameFavoriteAppController.java", "commit_hash": "f3c4d6ac20bcb3c9ff5c74f5109eb2104c09b860", "commit_date": "2025-07-23 19:07:36 +0800", "operation": "ADD"}, {"method": "POST", "path": "/user/center/info", "class_name": "AppUserCenterController", "method_name": "getUserCenterInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "36f72401ba263df093d7ec04b259412cd0e9687c", "commit_date": "2025-07-23 20:16:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/recordUserBehavior", "class_name": "AppUserCenterController", "method_name": "recordUser<PERSON>eh<PERSON>or", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "36f72401ba263df093d7ec04b259412cd0e9687c", "commit_date": "2025-07-23 20:16:02 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/info", "class_name": "AppUserCenterController", "method_name": "getUserCenterInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/recordUserBehavior", "class_name": "AppUserCenterController", "method_name": "recordUser<PERSON>eh<PERSON>or", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "d71398358da8a37fd3ffedb68ed16893498cae57", "commit_date": "2025-07-24 11:53:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserPracticeTextbookChapterList", "class_name": "AppQuestionController", "method_name": "getUserPracticeTextbookChapterList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "313233e8f52078fd78a39f7acbfdbafe9978e738", "commit_date": "2025-07-24 13:54:06 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/create", "class_name": "GameQuestionAdminController", "method_name": "createQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/game/question/update", "class_name": "GameQuestionAdminController", "method_name": "updateQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/game/question/delete", "class_name": "GameQuestionAdminController", "method_name": "deleteQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/get", "class_name": "GameQuestionAdminController", "method_name": "getQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/page", "class_name": "GameQuestionAdminController", "method_name": "getQuestionPage", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/game/question/batch-delete", "class_name": "GameQuestionAdminController", "method_name": "deleteByIds", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/game/question/batch-status", "class_name": "GameQuestionAdminController", "method_name": "batchUpdateStatus", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/export", "class_name": "GameQuestionAdminController", "method_name": "exportGameQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/import", "class_name": "GameQuestionAdminController", "method_name": "importQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/import/template", "class_name": "GameQuestionAdminController", "method_name": "downloadImportTemplate", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/check-delete", "class_name": "GameQuestionAdminController", "method_name": "checkQuestionCanDelete", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/check-hide", "class_name": "GameQuestionAdminController", "method_name": "checkQuestionCanHide", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/generate-pinyin", "class_name": "GameQuestionAdminController", "method_name": "generatePinyin", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "6a40f47d8f7bd87005199cb742db72640dfd76f2", "commit_date": "2025-07-24 15:06:40 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/submit-answer", "class_name": "GameAnswerRecordAppController", "method_name": "submitAnswer", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "****************************************", "commit_date": "2025-07-24 15:41:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/answer-report", "class_name": "GameAnswerRecordAppController", "method_name": "getAnswerReport", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "****************************************", "commit_date": "2025-07-24 15:41:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/create", "class_name": "TextbookController", "method_name": "createTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/update", "class_name": "TextbookController", "method_name": "updateTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/delete", "class_name": "TextbookController", "method_name": "deleteTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/get", "class_name": "TextbookController", "method_name": "getTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/page", "class_name": "TextbookController", "method_name": "getTextbookPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/pageChapter", "class_name": "TextbookController", "method_name": "getChapterPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getChapter", "class_name": "TextbookController", "method_name": "get<PERSON><PERSON>pter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/saveChapter", "class_name": "TextbookController", "method_name": "saveChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/deleteChapter", "class_name": "TextbookController", "method_name": "deleteChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getChapterHasQuestion", "class_name": "TextbookController", "method_name": "getChapterHasQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/", "class_name": "TextbookController", "method_name": "enableOrDisableChapter", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/deleteUnit", "class_name": "TextbookController", "method_name": "deleteUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/getUnitHasQuestion", "class_name": "TextbookController", "method_name": "getUnitHasQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/treeList", "class_name": "TextbookController", "method_name": "tree", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/textbook/mockTextbook", "class_name": "TextbookController", "method_name": "mockTextbook", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/textbook/TextbookController.java", "commit_hash": "0d0daf9b3a2c4db2944e97bf654acc8770fdc1a8", "commit_date": "2025-07-24 15:42:45 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/userOrderItemList", "class_name": "AppUserAdminController", "method_name": "userOrderItemList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/create", "class_name": "AppUserAdminController", "method_name": "createUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/user/update", "class_name": "AppUserAdminController", "method_name": "updateUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/user/delete", "class_name": "AppUserAdminController", "method_name": "deleteUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/user/update-status", "class_name": "AppUserAdminController", "method_name": "updateUserStatus", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/user/reset-password", "class_name": "AppUserAdminController", "method_name": "resetPassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/page", "class_name": "AppUserAdminController", "method_name": "getInteractiveCoursePage", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/get", "class_name": "AppUserAdminController", "method_name": "getUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/export", "class_name": "AppUserAdminController", "method_name": "exportUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/import/template", "class_name": "AppUserAdminController", "method_name": "downloadImportTemplate", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/import", "class_name": "AppUserAdminController", "method_name": "importUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java", "commit_hash": "3c7e23781274270d15488b6e8af71235c48d23dd", "commit_date": "2025-07-24 17:24:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course-category/create", "class_name": "EliteCourseCategoryController", "method_name": "createCourseCategory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "ea00a53ed4f605035c3df6babb162716a049005a", "commit_date": "2025-07-24 17:37:30 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course-category/update", "class_name": "EliteCourseCategoryController", "method_name": "updateCourseCategory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "ea00a53ed4f605035c3df6babb162716a049005a", "commit_date": "2025-07-24 17:37:30 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/elite-course-category/delete", "class_name": "EliteCourseCategoryController", "method_name": "deleteCourseCategory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "ea00a53ed4f605035c3df6babb162716a049005a", "commit_date": "2025-07-24 17:37:30 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course-category/check-delete", "class_name": "EliteCourseCategoryController", "method_name": "checkCourseCategoryCanDelete", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "ea00a53ed4f605035c3df6babb162716a049005a", "commit_date": "2025-07-24 17:37:30 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course-category/get", "class_name": "EliteCourseCategoryController", "method_name": "getCourseCategory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "ea00a53ed4f605035c3df6babb162716a049005a", "commit_date": "2025-07-24 17:37:30 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course-category/page", "class_name": "EliteCourseCategoryController", "method_name": "getCourseCategoryPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "ea00a53ed4f605035c3df6babb162716a049005a", "commit_date": "2025-07-24 17:37:30 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course-category/list", "class_name": "EliteCourseCategoryController", "method_name": "getAll", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "ea00a53ed4f605035c3df6babb162716a049005a", "commit_date": "2025-07-24 17:37:30 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course-category/list-by-hsk", "class_name": "EliteCourseCategoryController", "method_name": "getByHskLevel", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseCategoryController.java", "commit_hash": "ea00a53ed4f605035c3df6babb162716a049005a", "commit_date": "2025-07-24 17:37:30 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/create", "class_name": "EliteClassHourController", "method_name": "createEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "490fed75800f725efd886e416ecfb02c18094581", "commit_date": "2025-07-24 17:48:39 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-class-hour/update", "class_name": "EliteClassHourController", "method_name": "updateEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "490fed75800f725efd886e416ecfb02c18094581", "commit_date": "2025-07-24 17:48:39 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/elite-class-hour/delete", "class_name": "EliteClassHourController", "method_name": "deleteEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "490fed75800f725efd886e416ecfb02c18094581", "commit_date": "2025-07-24 17:48:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/get", "class_name": "EliteClassHourController", "method_name": "getEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "490fed75800f725efd886e416ecfb02c18094581", "commit_date": "2025-07-24 17:48:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/page", "class_name": "EliteClassHourController", "method_name": "getEliteClassHourPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "490fed75800f725efd886e416ecfb02c18094581", "commit_date": "2025-07-24 17:48:39 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-class-hour/sort", "class_name": "EliteClassHourController", "method_name": "updateEliteClassHourSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "490fed75800f725efd886e416ecfb02c18094581", "commit_date": "2025-07-24 17:48:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/reuse", "class_name": "EliteClassHourController", "method_name": "reuse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "490fed75800f725efd886e416ecfb02c18094581", "commit_date": "2025-07-24 17:48:39 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-class-hour/reuse", "class_name": "EliteClassHourController", "method_name": "getReuse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "490fed75800f725efd886e416ecfb02c18094581", "commit_date": "2025-07-24 17:48:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/create", "class_name": "EliteClassHourController", "method_name": "createEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "618af7af903f5bd884f64f8cc46dff15f37388ba", "commit_date": "2025-07-24 18:44:36 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-class-hour/update", "class_name": "EliteClassHourController", "method_name": "updateEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "618af7af903f5bd884f64f8cc46dff15f37388ba", "commit_date": "2025-07-24 18:44:36 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/elite-class-hour/delete", "class_name": "EliteClassHourController", "method_name": "deleteEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "618af7af903f5bd884f64f8cc46dff15f37388ba", "commit_date": "2025-07-24 18:44:36 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/get", "class_name": "EliteClassHourController", "method_name": "getEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "618af7af903f5bd884f64f8cc46dff15f37388ba", "commit_date": "2025-07-24 18:44:36 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/page", "class_name": "EliteClassHourController", "method_name": "getEliteClassHourPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "618af7af903f5bd884f64f8cc46dff15f37388ba", "commit_date": "2025-07-24 18:44:36 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-class-hour/sort", "class_name": "EliteClassHourController", "method_name": "updateEliteClassHourSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "618af7af903f5bd884f64f8cc46dff15f37388ba", "commit_date": "2025-07-24 18:44:36 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/reuse", "class_name": "EliteClassHourController", "method_name": "reuse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "618af7af903f5bd884f64f8cc46dff15f37388ba", "commit_date": "2025-07-24 18:44:36 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-class-hour/reuse", "class_name": "EliteClassHourController", "method_name": "getReuse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "618af7af903f5bd884f64f8cc46dff15f37388ba", "commit_date": "2025-07-24 18:44:36 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/create", "class_name": "EliteCourseController", "method_name": "createEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course/update", "class_name": "EliteCourseController", "method_name": "updateEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/elite-course/delete", "class_name": "EliteCourseController", "method_name": "deleteEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/check-delete", "class_name": "EliteCourseController", "method_name": "checkCourseCanDelete", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/get", "class_name": "EliteCourseController", "method_name": "getEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/page", "class_name": "EliteCourseController", "method_name": "getEliteCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/directory", "class_name": "EliteCourseController", "method_name": "getCourseDirectory", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course/update-status", "class_name": "EliteCourseController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course/update-listing-status", "class_name": "EliteCourseController", "method_name": "updateListingStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-course/sort", "class_name": "EliteCourseController", "method_name": "updateSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/export", "class_name": "EliteCourseController", "method_name": "exportEliteCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/overview", "class_name": "EliteCourseController", "method_name": "getCourseOverview", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteCourseController.java", "commit_hash": "aa357eae9cd0c0556ed2e3589de5beee521accdb", "commit_date": "2025-07-24 18:46:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/create", "class_name": "ExamAdminController", "method_name": "createExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "69a531739be378b9e8e764f5ad0f789b7358f848", "commit_date": "2025-07-24 18:48:27 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/exam/update", "class_name": "ExamAdminController", "method_name": "updateExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "69a531739be378b9e8e764f5ad0f789b7358f848", "commit_date": "2025-07-24 18:48:27 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/exam/delete", "class_name": "ExamAdminController", "method_name": "deleteExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "69a531739be378b9e8e764f5ad0f789b7358f848", "commit_date": "2025-07-24 18:48:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/get", "class_name": "ExamAdminController", "method_name": "getExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "69a531739be378b9e8e764f5ad0f789b7358f848", "commit_date": "2025-07-24 18:48:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/page", "class_name": "ExamAdminController", "method_name": "getExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "69a531739be378b9e8e764f5ad0f789b7358f848", "commit_date": "2025-07-24 18:48:27 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/exam/publish-status", "class_name": "ExamAdminController", "method_name": "updateExamPublishStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "69a531739be378b9e8e764f5ad0f789b7358f848", "commit_date": "2025-07-24 18:48:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/subject-list", "class_name": "ExamAdminController", "method_name": "getExamDetailSubjectList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "69a531739be378b9e8e764f5ad0f789b7358f848", "commit_date": "2025-07-24 18:48:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/random-compose", "class_name": "ExamAdminController", "method_name": "randomCompose", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "69a531739be378b9e8e764f5ad0f789b7358f848", "commit_date": "2025-07-24 18:48:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-paper-rule/create", "class_name": "ExamPaperRuleAdminController", "method_name": "createExamPaperRule", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamPaperRuleAdminController.java", "commit_hash": "c51e64e9927e46f80c5ba0e6c039eaf882a2b656", "commit_date": "2025-07-24 18:54:54 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/exam-paper-rule/update", "class_name": "ExamPaperRuleAdminController", "method_name": "updateExamPaperRule", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamPaperRuleAdminController.java", "commit_hash": "c51e64e9927e46f80c5ba0e6c039eaf882a2b656", "commit_date": "2025-07-24 18:54:54 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/exam-paper-rule/delete", "class_name": "ExamPaperRuleAdminController", "method_name": "deleteExamPaperRule", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamPaperRuleAdminController.java", "commit_hash": "c51e64e9927e46f80c5ba0e6c039eaf882a2b656", "commit_date": "2025-07-24 18:54:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-paper-rule/get", "class_name": "ExamPaperRuleAdminController", "method_name": "getExamPaperRule", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamPaperRuleAdminController.java", "commit_hash": "c51e64e9927e46f80c5ba0e6c039eaf882a2b656", "commit_date": "2025-07-24 18:54:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-paper-rule/page", "class_name": "ExamPaperRuleAdminController", "method_name": "getExamPaperRulePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamPaperRuleAdminController.java", "commit_hash": "c51e64e9927e46f80c5ba0e6c039eaf882a2b656", "commit_date": "2025-07-24 18:54:54 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/exam-paper-rule/update-status", "class_name": "ExamPaperRuleAdminController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamPaperRuleAdminController.java", "commit_hash": "c51e64e9927e46f80c5ba0e6c039eaf882a2b656", "commit_date": "2025-07-24 18:54:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-paper-rule/check-duplicate", "class_name": "ExamPaperRuleAdminController", "method_name": "checkExamPaperRuleDuplicate", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamPaperRuleAdminController.java", "commit_hash": "c51e64e9927e46f80c5ba0e6c039eaf882a2b656", "commit_date": "2025-07-24 18:54:54 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/exam-paper-rule/list-by-hsk", "class_name": "ExamPaperRuleAdminController", "method_name": "getBaseInfoListByHskLevel", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamPaperRuleAdminController.java", "commit_hash": "c51e64e9927e46f80c5ba0e6c039eaf882a2b656", "commit_date": "2025-07-24 18:54:54 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-question-type/create", "class_name": "ExamQuestionTypeAdminController", "method_name": "createExamQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamQuestionTypeAdminController.java", "commit_hash": "5c57a1600a96d2a02c504bff305e32a2d55af6ae", "commit_date": "2025-07-24 18:58:53 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/exam-question-type/update", "class_name": "ExamQuestionTypeAdminController", "method_name": "updateExamQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamQuestionTypeAdminController.java", "commit_hash": "5c57a1600a96d2a02c504bff305e32a2d55af6ae", "commit_date": "2025-07-24 18:58:53 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/exam-question-type/delete", "class_name": "ExamQuestionTypeAdminController", "method_name": "deleteExamQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamQuestionTypeAdminController.java", "commit_hash": "5c57a1600a96d2a02c504bff305e32a2d55af6ae", "commit_date": "2025-07-24 18:58:53 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-question-type/get", "class_name": "ExamQuestionTypeAdminController", "method_name": "getExamQuestionType", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamQuestionTypeAdminController.java", "commit_hash": "5c57a1600a96d2a02c504bff305e32a2d55af6ae", "commit_date": "2025-07-24 18:58:53 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-question-type/page", "class_name": "ExamQuestionTypeAdminController", "method_name": "getExamQuestionTypePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamQuestionTypeAdminController.java", "commit_hash": "5c57a1600a96d2a02c504bff305e32a2d55af6ae", "commit_date": "2025-07-24 18:58:53 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-question-type/check-delete", "class_name": "ExamQuestionTypeAdminController", "method_name": "checkExamQuestionTypeCanDelete", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamQuestionTypeAdminController.java", "commit_hash": "5c57a1600a96d2a02c504bff305e32a2d55af6ae", "commit_date": "2025-07-24 18:58:53 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam-question-type/check-duplicate", "class_name": "ExamQuestionTypeAdminController", "method_name": "checkExamQuestionTypeDuplicate", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamQuestionTypeAdminController.java", "commit_hash": "5c57a1600a96d2a02c504bff305e32a2d55af6ae", "commit_date": "2025-07-24 18:58:53 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/create", "class_name": "InteractiveCourseUnitController", "method_name": "createInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "7f8b55174cf13cc72de683d188eff3b2426a374e", "commit_date": "2025-07-24 19:09:10 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "7f8b55174cf13cc72de683d188eff3b2426a374e", "commit_date": "2025-07-24 19:09:10 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/interactive-course-unit/delete", "class_name": "InteractiveCourseUnitController", "method_name": "deleteInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "7f8b55174cf13cc72de683d188eff3b2426a374e", "commit_date": "2025-07-24 19:09:10 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/interactive-course-unit/get/v2", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitV2", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "7f8b55174cf13cc72de683d188eff3b2426a374e", "commit_date": "2025-07-24 19:09:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/page", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "7f8b55174cf13cc72de683d188eff3b2426a374e", "commit_date": "2025-07-24 19:09:10 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-status/{id}", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "7f8b55174cf13cc72de683d188eff3b2426a374e", "commit_date": "2025-07-24 19:09:10 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-sort", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "7f8b55174cf13cc72de683d188eff3b2426a374e", "commit_date": "2025-07-24 19:09:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/export", "class_name": "InteractiveCourseUnitController", "method_name": "exportInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "7f8b55174cf13cc72de683d188eff3b2426a374e", "commit_date": "2025-07-24 19:09:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/trade/order/remark/create", "class_name": "OrderRemarkController", "method_name": "createOrderRemark", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/controller/admin/order/OrderRemarkController.java", "commit_hash": "2c181232e5c8890ef04a301c7198191f5a042934", "commit_date": "2025-07-24 19:10:20 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/trade/order/remark/list", "class_name": "OrderRemarkController", "method_name": "getOrderRemarkList", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/controller/admin/order/OrderRemarkController.java", "commit_hash": "2c181232e5c8890ef04a301c7198191f5a042934", "commit_date": "2025-07-24 19:10:20 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/third-party/you-dao/text-to-speech", "class_name": "YouDaoAdminController", "method_name": "textToSpeech", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/controller/admin/youdao/YouDaoAdminController.java", "commit_hash": "567f5fff95c8696e52f41bb86f76694c421e2132", "commit_date": "2025-07-24 19:13:47 +0800", "operation": "ADD"}, {"method": "POST", "path": "/game/special-exercise/create", "class_name": "SpecialExerciseAdminController", "method_name": "createSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/game/special-exercise/update", "class_name": "SpecialExerciseAdminController", "method_name": "updateSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/game/special-exercise/batch-delete", "class_name": "SpecialExerciseAdminController", "method_name": "deleteByIds", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/get", "class_name": "SpecialExerciseAdminController", "method_name": "getSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/page", "class_name": "SpecialExerciseAdminController", "method_name": "getSpecialExercisePage", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/game/special-exercise/batch-status", "class_name": "SpecialExerciseAdminController", "method_name": "batchUpdateStatus", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/game/special-exercise/sort", "class_name": "SpecialExerciseAdminController", "method_name": "updateSort", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/export", "class_name": "SpecialExerciseAdminController", "method_name": "exportSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/import", "class_name": "SpecialExerciseAdminController", "method_name": "importSpecialExercise", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/import/template", "class_name": "SpecialExerciseAdminController", "method_name": "downloadImportTemplate", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/special-exercise/check-delete", "class_name": "SpecialExerciseAdminController", "method_name": "checkSpecialExerciseCanDelete", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/specialexercise/SpecialExerciseAdminController.java", "commit_hash": "ea59b5a9767a9ace06fda08d4a9786ea4ba3e63d", "commit_date": "2025-07-24 19:16:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/create", "class_name": "TagController", "method_name": "createTag", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/tag/update", "class_name": "TagController", "method_name": "updateTag", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/delete", "class_name": "TagController", "method_name": "deleteTag", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/pageTagRef", "class_name": "TagController", "method_name": "getTagRef", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/delete/batch", "class_name": "TagController", "method_name": "deleteTagBatch", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/get/list", "class_name": "TagController", "method_name": "getTagList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/get", "class_name": "TagController", "method_name": "getTag", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/page", "class_name": "TagController", "method_name": "getTagPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/import/template", "class_name": "TagController", "method_name": "downloadImportTemplate", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/tag/import", "class_name": "TagController", "method_name": "importTag", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/tag/TagController.java", "commit_hash": "6df96793bc425fd0896978b72bd6a36b0c3d1c0f", "commit_date": "2025-07-24 19:19:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/teacher/create", "class_name": "TeacherController", "method_name": "create<PERSON><PERSON>er", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/teacher/update", "class_name": "TeacherController", "method_name": "update<PERSON><PERSON>er", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/teacher/delete", "class_name": "TeacherController", "method_name": "deleteTeacher", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/teacher/get", "class_name": "TeacherController", "method_name": "<PERSON><PERSON><PERSON>er", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/teacher/page", "class_name": "TeacherController", "method_name": "getTeacherPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/teacher/update-status", "class_name": "TeacherController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/teacher/update-sort", "class_name": "TeacherController", "method_name": "updateSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/teacher/export", "class_name": "TeacherController", "method_name": "exportTeacher", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/teacher/export-course", "class_name": "TeacherController", "method_name": "exportTeacherCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/teacher/TeacherController.java", "commit_hash": "0b32a374058797d5b6ebc3dcee4f849948d04fcc", "commit_date": "2025-07-24 19:20:49 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/create", "class_name": "WordController", "method_name": "createWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/word/update", "class_name": "WordController", "method_name": "updateWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/delete", "class_name": "WordController", "method_name": "deleteWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/get", "class_name": "WordController", "method_name": "getWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/page", "class_name": "WordController", "method_name": "getWordPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/getQuoteInfo", "class_name": "WordController", "method_name": "getQuoteInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/export", "class_name": "WordController", "method_name": "exportWord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/getSpecialExerciseQuote", "class_name": "WordController", "method_name": "getQuestionInfoListByWordId", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/getInteractiveCourseQuote", "class_name": "WordController", "method_name": "getInteractiveCourseQuoteByWordId", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/word/getSpecialExerciseQuoteInfo", "class_name": "WordController", "method_name": "getSpecialExerciseQuoteInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/word/WordController.java", "commit_hash": "0fc62ad0d3ec9dafe0e7afb5e8572dfc29676092", "commit_date": "2025-07-24 19:23:11 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendLoginSmsCode", "class_name": "SmsController", "method_name": "sendLoginSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "20e82d76620712ef4af042d0c1d6bf0284285cdb", "commit_date": "2025-07-24 19:29:08 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendResetPasswordSmsCode", "class_name": "SmsController", "method_name": "sendResetPasswordSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "20e82d76620712ef4af042d0c1d6bf0284285cdb", "commit_date": "2025-07-24 19:29:08 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendChangeMobileCode", "class_name": "SmsController", "method_name": "sendChangeMobileCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "20e82d76620712ef4af042d0c1d6bf0284285cdb", "commit_date": "2025-07-24 19:29:08 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeTextbookChapterList", "class_name": "AppQuestionController", "method_name": "getUserPracticeTextbookChapterList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeUnitQuestionTypeList", "class_name": "AppQuestionController", "method_name": "getUserPracticeUnitQuestionTypeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "ac20a54403243643f01c7907c7af53724d1c92e3", "commit_date": "2025-07-25 11:36:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail/create", "class_name": "QuestionDetailController", "method_name": "createQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-detail/update", "class_name": "QuestionDetailController", "method_name": "updateQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail/delete", "class_name": "QuestionDetailController", "method_name": "deleteQuestionDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetail/QuestionDetailController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/create", "class_name": "QuestionDetailVersionController", "method_name": "createQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-detail-version/update", "class_name": "QuestionDetailVersionController", "method_name": "updateQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/delete", "class_name": "QuestionDetailVersionController", "method_name": "deleteQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-detail-version/get", "class_name": "QuestionDetailVersionController", "method_name": "getQuestionDetailVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questiondetailversion/QuestionDetailVersionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/create", "class_name": "QuestionVersionController", "method_name": "createQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question-version/update", "class_name": "QuestionVersionController", "method_name": "updateQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/delete", "class_name": "QuestionVersionController", "method_name": "deleteQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question-version/get", "class_name": "QuestionVersionController", "method_name": "getQuestionVersion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/questionversion/QuestionVersionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeTextbookChapterList", "class_name": "AppQuestionController", "method_name": "getUserPracticeTextbookChapterList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeUnitQuestionTypeList", "class_name": "AppQuestionController", "method_name": "getUserPracticeUnitQuestionTypeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/info", "class_name": "AppUserCenterController", "method_name": "getUserCenterInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/recordUserBehavior", "class_name": "AppUserCenterController", "method_name": "recordUser<PERSON>eh<PERSON>or", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/submit-answer", "class_name": "GameAnswerRecordAppController", "method_name": "submitAnswer", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "506181d587788cda42de92a6f6d58d5123cfd166", "commit_date": "2025-07-25 13:41:38 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/answer-report", "class_name": "GameAnswerRecordAppController", "method_name": "getAnswerReport", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "506181d587788cda42de92a6f6d58d5123cfd166", "commit_date": "2025-07-25 13:41:38 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendLoginSmsCode", "class_name": "SmsController", "method_name": "sendLoginSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "7fe37ba22876afef825adca0094e151e8efe9612", "commit_date": "2025-07-25 13:54:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendResetPasswordSmsCode", "class_name": "SmsController", "method_name": "sendResetPasswordSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "7fe37ba22876afef825adca0094e151e8efe9612", "commit_date": "2025-07-25 13:54:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendChangeMobileCode", "class_name": "SmsController", "method_name": "sendChangeMobileCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "7fe37ba22876afef825adca0094e151e8efe9612", "commit_date": "2025-07-25 13:54:12 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/info", "class_name": "AppUserCenterController", "method_name": "getUserCenterInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "****************************************", "commit_date": "2025-07-25 14:00:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/center/recordUserBehavior", "class_name": "AppUserCenterController", "method_name": "recordUser<PERSON>eh<PERSON>or", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserCenterController.java", "commit_hash": "****************************************", "commit_date": "2025-07-25 14:00:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/gameQuestionsList", "class_name": "UserFavoriteAppController", "method_name": "gameQuestionsList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "c0bd3160e96aa255a96df629bc927ddd2c6d847c", "commit_date": "2025-07-25 15:17:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/toggle", "class_name": "UserFavoriteAppController", "method_name": "toggleFavorite", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "c0bd3160e96aa255a96df629bc927ddd2c6d847c", "commit_date": "2025-07-25 15:17:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendLoginSmsCode", "class_name": "SmsController", "method_name": "sendLoginSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendResetPasswordSmsCode", "class_name": "SmsController", "method_name": "sendResetPasswordSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendChangeMobileCode", "class_name": "SmsController", "method_name": "sendChangeMobileCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendNewMobileCode", "class_name": "SmsController", "method_name": "sendNewMobileCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//getUserInfo", "class_name": "AppUserController", "method_name": "getUserInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//logout", "class_name": "AppUserController", "method_name": "logout", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//setPassword", "class_name": "AppUserController", "method_name": "setPassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//logoutUser", "class_name": "AppUserController", "method_name": "logoutUser", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//setUserInfo", "class_name": "AppUserController", "method_name": "setUserInfo", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//updateUserArea", "class_name": "AppUserController", "method_name": "updateUserArea", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//updatePassword", "class_name": "AppUserController", "method_name": "updatePassword", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//verifyOldMobile", "class_name": "AppUserController", "method_name": "verifyOldMobile", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//changeMobile", "class_name": "AppUserController", "method_name": "changeMobile", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user//getChangeMobileStatus", "class_name": "AppUserController", "method_name": "getChangeMobileStatus", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/user/AppUserController.java", "commit_hash": "0917c753a6a9b7f2adbd35da44ceb6c422a70f95", "commit_date": "2025-07-28 11:22:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/create", "class_name": "QuestionController", "method_name": "createQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/question/update", "class_name": "QuestionController", "method_name": "updateQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/updateStatus", "class_name": "QuestionController", "method_name": "updateStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/get", "class_name": "QuestionController", "method_name": "getQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getByQuestionId", "class_name": "QuestionController", "method_name": "getByQuestionId", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/page", "class_name": "QuestionController", "method_name": "getQuestionPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/import/template", "class_name": "QuestionController", "method_name": "downloadImportTemplate", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/executeData", "class_name": "QuestionController", "method_name": "executeData", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/import", "class_name": "QuestionController", "method_name": "importQuestion", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/export", "class_name": "QuestionController", "method_name": "export", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/testXunFeiUpload", "class_name": "QuestionController", "method_name": "testXunFeiUpload", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/testXunFeiCallback", "class_name": "QuestionController", "method_name": "testXunFeiCallback", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/testXunFeiGetResult", "class_name": "QuestionController", "method_name": "testXunFeiGetResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/question/QuestionController.java", "commit_hash": "5b1587841942a45d14b518a1578eb8b139843bcb", "commit_date": "2025-07-28 14:23:29 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/category/list", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "5b704094367aba8af3ad054fd042ce328ae23376", "commit_date": "2025-07-28 17:31:17 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/hsk/category/list", "class_name": "AppEliteCourseController", "method_name": "getHskLevelCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "5b704094367aba8af3ad054fd042ce328ae23376", "commit_date": "2025-07-28 17:31:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/page", "class_name": "AppEliteCourseController", "method_name": "getEliteCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "5b704094367aba8af3ad054fd042ce328ae23376", "commit_date": "2025-07-28 17:31:17 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/detail/{id}", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "5b704094367aba8af3ad054fd042ce328ae23376", "commit_date": "2025-07-28 17:31:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/myCourse", "class_name": "AppEliteCourseController", "method_name": "getMyCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "5b704094367aba8af3ad054fd042ce328ae23376", "commit_date": "2025-07-28 17:31:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/gameQuestionsList", "class_name": "UserFavoriteAppController", "method_name": "gameQuestionsList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "096dc4b1aa61d9e13284873f667b0bea487c7714", "commit_date": "2025-07-28 20:01:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/toggle", "class_name": "UserFavoriteAppController", "method_name": "toggleFavorite", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "096dc4b1aa61d9e13284873f667b0bea487c7714", "commit_date": "2025-07-28 20:01:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/question-list", "class_name": "UserFavoriteAppController", "method_name": "questionList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "096dc4b1aa61d9e13284873f667b0bea487c7714", "commit_date": "2025-07-28 20:01:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/create", "class_name": "GameQuestionAdminController", "method_name": "createQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/game/question/update", "class_name": "GameQuestionAdminController", "method_name": "updateQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/game/question/delete", "class_name": "GameQuestionAdminController", "method_name": "deleteQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/get", "class_name": "GameQuestionAdminController", "method_name": "getQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/page", "class_name": "GameQuestionAdminController", "method_name": "getQuestionPage", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/game/question/batch-delete", "class_name": "GameQuestionAdminController", "method_name": "deleteByIds", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/game/question/batch-status", "class_name": "GameQuestionAdminController", "method_name": "batchUpdateStatus", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/export", "class_name": "GameQuestionAdminController", "method_name": "exportGameQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/import", "class_name": "GameQuestionAdminController", "method_name": "importQuestion", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/import/template", "class_name": "GameQuestionAdminController", "method_name": "downloadImportTemplate", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/check-delete", "class_name": "GameQuestionAdminController", "method_name": "checkQuestionCanDelete", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/check-hide", "class_name": "GameQuestionAdminController", "method_name": "checkQuestionCanHide", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/question/generate-pinyin", "class_name": "GameQuestionAdminController", "method_name": "generatePinyin", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/admin/question/GameQuestionAdminController.java", "commit_hash": "372d57b84193adaffa7ceb3140597d4eb58db337", "commit_date": "2025-07-29 10:23:14 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/gameQuestionsList", "class_name": "UserFavoriteAppController", "method_name": "gameQuestionsList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "75d5064a573d9a90deb1a9af96a61d4513ea6529", "commit_date": "2025-07-29 14:30:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/toggle", "class_name": "UserFavoriteAppController", "method_name": "toggleFavorite", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "75d5064a573d9a90deb1a9af96a61d4513ea6529", "commit_date": "2025-07-29 14:30:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/question-list", "class_name": "UserFavoriteAppController", "method_name": "questionList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "75d5064a573d9a90deb1a9af96a61d4513ea6529", "commit_date": "2025-07-29 14:30:17 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/favorite/word-book", "class_name": "UserFavoriteAppController", "method_name": "wordBookList", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/favorite/UserFavoriteAppController.java", "commit_hash": "75d5064a573d9a90deb1a9af96a61d4513ea6529", "commit_date": "2025-07-29 14:30:17 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/category/list", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "b914831c13189df803c3c8dc60cdecad04106a26", "commit_date": "2025-07-29 14:42:37 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/hsk/category/list", "class_name": "AppEliteCourseController", "method_name": "getHskLevelCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "b914831c13189df803c3c8dc60cdecad04106a26", "commit_date": "2025-07-29 14:42:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/page", "class_name": "AppEliteCourseController", "method_name": "getEliteCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "b914831c13189df803c3c8dc60cdecad04106a26", "commit_date": "2025-07-29 14:42:37 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/detail/{id}", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "b914831c13189df803c3c8dc60cdecad04106a26", "commit_date": "2025-07-29 14:42:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/myCourse", "class_name": "AppEliteCourseController", "method_name": "getMyCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "b914831c13189df803c3c8dc60cdecad04106a26", "commit_date": "2025-07-29 14:42:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/getCourseChapterInfo", "class_name": "AppEliteCourseController", "method_name": "getCourseChapterInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "b914831c13189df803c3c8dc60cdecad04106a26", "commit_date": "2025-07-29 14:42:37 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/infra/config/create", "class_name": "ConfigController", "method_name": "createConfig", "file_path": "hsk-module-infra/hsk-module-infra-biz/src/main/java/com/xt/hsk/module/infra/controller/admin/config/ConfigController.java", "commit_hash": "6577cf05a6bd5c086de5abd1b03afaa2fb2b942d", "commit_date": "2025-07-29 16:17:39 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/infra/config/update", "class_name": "ConfigController", "method_name": "updateConfig", "file_path": "hsk-module-infra/hsk-module-infra-biz/src/main/java/com/xt/hsk/module/infra/controller/admin/config/ConfigController.java", "commit_hash": "6577cf05a6bd5c086de5abd1b03afaa2fb2b942d", "commit_date": "2025-07-29 16:17:39 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/infra/config/delete", "class_name": "ConfigController", "method_name": "deleteConfig", "file_path": "hsk-module-infra/hsk-module-infra-biz/src/main/java/com/xt/hsk/module/infra/controller/admin/config/ConfigController.java", "commit_hash": "6577cf05a6bd5c086de5abd1b03afaa2fb2b942d", "commit_date": "2025-07-29 16:17:39 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/infra/config/get", "class_name": "ConfigController", "method_name": "getConfig", "file_path": "hsk-module-infra/hsk-module-infra-biz/src/main/java/com/xt/hsk/module/infra/controller/admin/config/ConfigController.java", "commit_hash": "6577cf05a6bd5c086de5abd1b03afaa2fb2b942d", "commit_date": "2025-07-29 16:17:39 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/infra/config/get-value-by-key", "class_name": "ConfigController", "method_name": "getConfigKey", "file_path": "hsk-module-infra/hsk-module-infra-biz/src/main/java/com/xt/hsk/module/infra/controller/admin/config/ConfigController.java", "commit_hash": "6577cf05a6bd5c086de5abd1b03afaa2fb2b942d", "commit_date": "2025-07-29 16:17:39 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/infra/config/page", "class_name": "ConfigController", "method_name": "getConfigPage", "file_path": "hsk-module-infra/hsk-module-infra-biz/src/main/java/com/xt/hsk/module/infra/controller/admin/config/ConfigController.java", "commit_hash": "6577cf05a6bd5c086de5abd1b03afaa2fb2b942d", "commit_date": "2025-07-29 16:17:39 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/infra/config/export", "class_name": "ConfigController", "method_name": "exportConfig", "file_path": "hsk-module-infra/hsk-module-infra-biz/src/main/java/com/xt/hsk/module/infra/controller/admin/config/ConfigController.java", "commit_hash": "6577cf05a6bd5c086de5abd1b03afaa2fb2b942d", "commit_date": "2025-07-29 16:17:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "0be2d7543cc72144b6fd209d72f9754267c7c8f5", "commit_date": "2025-07-29 17:19:27 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/category/list", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "8979c72c41d5a6c842550e214625aeae4b766acb", "commit_date": "2025-07-29 19:20:52 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/hsk/category/list", "class_name": "AppEliteCourseController", "method_name": "getHskLevelCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "8979c72c41d5a6c842550e214625aeae4b766acb", "commit_date": "2025-07-29 19:20:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/page", "class_name": "AppEliteCourseController", "method_name": "getEliteCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "8979c72c41d5a6c842550e214625aeae4b766acb", "commit_date": "2025-07-29 19:20:52 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/detail/{id}", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "8979c72c41d5a6c842550e214625aeae4b766acb", "commit_date": "2025-07-29 19:20:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/myCourse", "class_name": "AppEliteCourseController", "method_name": "getMyCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "8979c72c41d5a6c842550e214625aeae4b766acb", "commit_date": "2025-07-29 19:20:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/getCourseChapterInfo", "class_name": "AppEliteCourseController", "method_name": "getCourseChapterInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "8979c72c41d5a6c842550e214625aeae4b766acb", "commit_date": "2025-07-29 19:20:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/getStudyRecord", "class_name": "AppEliteCourseController", "method_name": "getStudyRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "8979c72c41d5a6c842550e214625aeae4b766acb", "commit_date": "2025-07-29 19:20:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/saveStudyRecord", "class_name": "AppEliteCourseController", "method_name": "saveStudyRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "8979c72c41d5a6c842550e214625aeae4b766acb", "commit_date": "2025-07-29 19:20:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46984e6822f4c3c7eb3c0f5ff00c151ac51b441b", "commit_date": "2025-07-30 13:22:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeTextbookChapterList", "class_name": "AppQuestionController", "method_name": "getUserPracticeTextbookChapterList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeUnitQuestionTypeList", "class_name": "AppQuestionController", "method_name": "getUserPracticeUnitQuestionTypeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "097b1a918e3a650a764a3c8a04f9dd3194e7abe5", "commit_date": "2025-07-30 16:56:15 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/submit-answer", "class_name": "GameAnswerRecordAppController", "method_name": "submitAnswer", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "33ddd4b8f2f344df036af304e0296d6783246975", "commit_date": "2025-07-31 16:47:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/answer-report", "class_name": "GameAnswerRecordAppController", "method_name": "getAnswerReport", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "33ddd4b8f2f344df036af304e0296d6783246975", "commit_date": "2025-07-31 16:47:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/game/record/save-current-round", "class_name": "GameAnswerRecordAppController", "method_name": "saveCurrentRound", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/controller/app/record/GameAnswerRecordAppController.java", "commit_hash": "33ddd4b8f2f344df036af304e0296d6783246975", "commit_date": "2025-07-31 16:47:27 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeTextbookChapterList", "class_name": "AppQuestionController", "method_name": "getUserPracticeTextbookChapterList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeUnitQuestionTypeList", "class_name": "AppQuestionController", "method_name": "getUserPracticeUnitQuestionTypeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/levelSelfTest", "class_name": "AppQuestionController", "method_name": "levelSelfTest", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "75d8fd0e3eb525fa01d1e2d61ce053ca79fd444c", "commit_date": "2025-07-31 20:24:35 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionStatistics", "class_name": "AppQuestionController", "method_name": "getQuestionStatistics", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeCountList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeCountList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionPracticeList", "class_name": "AppQuestionController", "method_name": "getQuestionPracticeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/hasUnfinished<PERSON>ratice", "class_name": "AppQuestionController", "method_name": "hasUnfinishedPratice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/startPractice", "class_name": "AppQuestionController", "method_name": "startPractice", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionDetails", "class_name": "AppQuestionController", "method_name": "getQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getStudyReport", "class_name": "AppQuestionController", "method_name": "getStudyReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getReportQuestionDetails", "class_name": "AppQuestionController", "method_name": "getReportQuestionDetails", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/saveUserQuestionAnswerRecord", "class_name": "AppQuestionController", "method_name": "saveUserQuestionAnswerRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/commitUserPracticeEnd", "class_name": "AppQuestionController", "method_name": "commitUserPracticeEnd", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/oneClickPolishing", "class_name": "AppQuestionController", "method_name": "oneClickPolishing", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/question/getUserQuestionAiCorrectionCount", "class_name": "AppQuestionController", "method_name": "getUserQuestionAiCorrectionCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/questionCallAiCorrection", "class_name": "AppQuestionController", "method_name": "questionCallAiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getQuestionAiCorrectionResult", "class_name": "AppQuestionController", "method_name": "getQuestionAiCorrectionResult", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeTextbookChapterList", "class_name": "AppQuestionController", "method_name": "getUserPracticeTextbookChapterList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/question/getUserPracticeUnitQuestionTypeList", "class_name": "AppQuestionController", "method_name": "getUserPracticeUnitQuestionTypeList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/question/AppQuestionController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-self-assessment-record/start", "class_name": "UserSelfAssessmentRecordController", "method_name": "createUserSelfAssessmentRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/userselfassessmentrecord/UserSelfAssessmentRecordController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "ADD"}, {"method": "PUT", "path": "/edu/user-self-assessment-record/submit", "class_name": "UserSelfAssessmentRecordController", "method_name": "submitUserSelfAssessmentRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/userselfassessmentrecord/UserSelfAssessmentRecordController.java", "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "ADD"}, {"method": "POST", "path": "/edu/user-self-assessment-record/start", "class_name": "UserSelfAssessmentRecordController", "method_name": "createUserSelfAssessmentRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/userselfassessmentrecord/UserSelfAssessmentRecordController.java", "commit_hash": "26763bfba17e19c8e54202ee73489e4d2255c57a", "commit_date": "2025-08-01 18:27:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-self-assessment-record/submit", "class_name": "UserSelfAssessmentRecordController", "method_name": "submitUserSelfAssessmentRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/userselfassessmentrecord/UserSelfAssessmentRecordController.java", "commit_hash": "26763bfba17e19c8e54202ee73489e4d2255c57a", "commit_date": "2025-08-01 18:27:00 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendLoginSmsCode", "class_name": "SmsController", "method_name": "sendLoginSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "c1a068caa6e370484d45d476ed787981f326f817", "commit_date": "2025-08-02 13:38:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendResetPasswordSmsCode", "class_name": "SmsController", "method_name": "sendResetPasswordSmsCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "c1a068caa6e370484d45d476ed787981f326f817", "commit_date": "2025-08-02 13:38:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendChangeMobileCode", "class_name": "SmsController", "method_name": "sendChangeMobileCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "c1a068caa6e370484d45d476ed787981f326f817", "commit_date": "2025-08-02 13:38:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/user/sms/sendNewMobileCode", "class_name": "SmsController", "method_name": "sendNewMobileCode", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/app/sms/SmsController.java", "commit_hash": "c1a068caa6e370484d45d476ed787981f326f817", "commit_date": "2025-08-02 13:38:33 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "a40a1f32a944c4cbfac595d31196d034c9ac676c", "commit_date": "2025-08-03 19:14:39 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c6819b5de3522a57ce84ad683be1c5377021f3d1", "commit_date": "2025-08-03 19:15:04 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/metadata", "class_name": "ExamAppController", "method_name": "getAppExamSubjectInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "b8ce20c0d91f4edd81f8317f8a05a99e356a9748", "commit_date": "2025-08-03 19:26:18 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "75460aa3f0ce4fc3530a8884d6701a374b9222c9", "commit_date": "2025-08-03 19:26:48 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/ai-correction", "class_name": "ExamAppController", "method_name": "aiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "c124bee4890f76673e98a83d1557c3103e0ef887", "commit_date": "2025-08-03 23:52:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/create", "class_name": "InteractiveCourseUnitController", "method_name": "createInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "9efcc476e8374f8984faf4dc38a0b53ffb0f11c0", "commit_date": "2025-08-04 13:17:53 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "9efcc476e8374f8984faf4dc38a0b53ffb0f11c0", "commit_date": "2025-08-04 13:17:53 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/interactive-course-unit/delete", "class_name": "InteractiveCourseUnitController", "method_name": "deleteInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "9efcc476e8374f8984faf4dc38a0b53ffb0f11c0", "commit_date": "2025-08-04 13:17:53 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/interactive-course-unit/get/v2", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitV2", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "9efcc476e8374f8984faf4dc38a0b53ffb0f11c0", "commit_date": "2025-08-04 13:17:53 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/page", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "9efcc476e8374f8984faf4dc38a0b53ffb0f11c0", "commit_date": "2025-08-04 13:17:53 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-status/{id}", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "9efcc476e8374f8984faf4dc38a0b53ffb0f11c0", "commit_date": "2025-08-04 13:17:53 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-sort", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "9efcc476e8374f8984faf4dc38a0b53ffb0f11c0", "commit_date": "2025-08-04 13:17:53 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/export", "class_name": "InteractiveCourseUnitController", "method_name": "exportInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "9efcc476e8374f8984faf4dc38a0b53ffb0f11c0", "commit_date": "2025-08-04 13:17:53 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/create", "class_name": "ExamAdminController", "method_name": "createExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/exam/update", "class_name": "ExamAdminController", "method_name": "updateExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/exam/delete", "class_name": "ExamAdminController", "method_name": "deleteExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/get", "class_name": "ExamAdminController", "method_name": "getExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/page", "class_name": "ExamAdminController", "method_name": "getExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/exam/publish-status", "class_name": "ExamAdminController", "method_name": "updateExamPublishStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/subject-list", "class_name": "ExamAdminController", "method_name": "getExamDetailSubjectList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/random-compose", "class_name": "ExamAdminController", "method_name": "randomCompose", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/exam/preview", "class_name": "ExamAdminController", "method_name": "getExamPreview", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/exam/ExamAdminController.java", "commit_hash": "d701b1e6e829115047b3bff8b02cd2297002475d", "commit_date": "2025-08-04 16:44:34 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-exam-record/report", "class_name": "UserExamRecordAppController", "method_name": "getExamReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/UserExamRecordAppController.java", "commit_hash": "4b75b3d20b8c3f61992cab2bc7476d1d3b64354a", "commit_date": "2025-08-05 11:09:45 +0800", "operation": "ADD"}, {"method": "POST", "path": "/edu/user-exam-record/report", "class_name": "UserExamRecordAppController", "method_name": "getExamReport", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/UserExamRecordAppController.java", "commit_hash": "28be6aca92b31b3d26fd77db4e9cbdda029de8cb", "commit_date": "2025-08-05 11:34:59 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-self-assessment-record/start", "class_name": "UserSelfAssessmentRecordController", "method_name": "createUserSelfAssessmentRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/userselfassessmentrecord/UserSelfAssessmentRecordController.java", "commit_hash": "47911c17ce1f129c0a562719684bd32227dfdaac", "commit_date": "2025-08-05 17:55:21 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-self-assessment-record/submit", "class_name": "UserSelfAssessmentRecordController", "method_name": "submitUserSelfAssessmentRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/userselfassessmentrecord/UserSelfAssessmentRecordController.java", "commit_hash": "47911c17ce1f129c0a562719684bd32227dfdaac", "commit_date": "2025-08-05 17:55:21 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-self-assessment-record/setQuestions", "class_name": "UserSelfAssessmentRecordController", "method_name": "setQuestions", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/userselfassessmentrecord/UserSelfAssessmentRecordController.java", "commit_hash": "47911c17ce1f129c0a562719684bd32227dfdaac", "commit_date": "2025-08-05 17:55:21 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/ai-correction", "class_name": "ExamAppController", "method_name": "aiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "aa9826438374867978a9f0de6b453243df6ebd9c", "commit_date": "2025-08-05 19:38:22 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-exam/answer-progress/v1/save", "class_name": "UserExamAnswerProgressAppController", "method_name": "save", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/UserExamAnswerProgressAppController.java", "commit_hash": "55506cbce17552d5d920978890bf569a5c9a7123", "commit_date": "2025-08-05 22:43:10 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-exam/answer-progress/v1/save", "class_name": "UserExamAnswerProgressAppController", "method_name": "save", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/UserExamAnswerProgressAppController.java", "commit_hash": "eff474c7412c8bcd51d062b99605242afd2d24dd", "commit_date": "2025-08-05 22:47:25 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/user-exam/answer-progress/v1/get", "class_name": "UserExamAnswerProgressAppController", "method_name": "get", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/UserExamAnswerProgressAppController.java", "commit_hash": "eff474c7412c8bcd51d062b99605242afd2d24dd", "commit_date": "2025-08-05 22:47:25 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/create", "class_name": "EliteClassHourController", "method_name": "createEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-class-hour/update", "class_name": "EliteClassHourController", "method_name": "updateEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/elite-class-hour/delete", "class_name": "EliteClassHourController", "method_name": "deleteEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/get", "class_name": "EliteClassHourController", "method_name": "getEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/page", "class_name": "EliteClassHourController", "method_name": "getEliteClassHourPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-class-hour/sort", "class_name": "EliteClassHourController", "method_name": "updateEliteClassHourSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/reuse", "class_name": "EliteClassHourController", "method_name": "reuse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-class-hour/reuse", "class_name": "EliteClassHourController", "method_name": "getReuse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/video-info", "class_name": "EliteClassHourController", "method_name": "getEliteClassHourVideoInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "8088ef3e32881e1533b5b0aad73d3a48d85be8da", "commit_date": "2025-08-05 22:58:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/create", "class_name": "EliteClassHourController", "method_name": "createEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-class-hour/update", "class_name": "EliteClassHourController", "method_name": "updateEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/elite-class-hour/delete", "class_name": "EliteClassHourController", "method_name": "deleteEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/get", "class_name": "EliteClassHourController", "method_name": "getEliteClassHour", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/page", "class_name": "EliteClassHourController", "method_name": "getEliteClassHourPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/elite-class-hour/sort", "class_name": "EliteClassHourController", "method_name": "updateEliteClassHourSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/reuse", "class_name": "EliteClassHourController", "method_name": "reuse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-class-hour/reuse", "class_name": "EliteClassHourController", "method_name": "getReuse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-class-hour/video-info", "class_name": "EliteClassHourController", "method_name": "getEliteClassHourVideoInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/elitecourse/EliteClassHourController.java", "commit_hash": "da41b2b4da1633c5c5441c75affb3190b41f6a9b", "commit_date": "2025-08-06 11:50:57 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/create", "class_name": "InteractiveCourseUnitController", "method_name": "createInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "e3c4b774ba911fcda9da793f0fa095497f70f8f9", "commit_date": "2025-08-06 14:23:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "e3c4b774ba911fcda9da793f0fa095497f70f8f9", "commit_date": "2025-08-06 14:23:31 +0800", "operation": "MODIFY"}, {"method": "DELETE", "path": "/edu/interactive-course-unit/delete", "class_name": "InteractiveCourseUnitController", "method_name": "deleteInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "e3c4b774ba911fcda9da793f0fa095497f70f8f9", "commit_date": "2025-08-06 14:23:31 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/interactive-course-unit/get/v2", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitV2", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "e3c4b774ba911fcda9da793f0fa095497f70f8f9", "commit_date": "2025-08-06 14:23:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/page", "class_name": "InteractiveCourseUnitController", "method_name": "getInteractiveCourseUnitPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "e3c4b774ba911fcda9da793f0fa095497f70f8f9", "commit_date": "2025-08-06 14:23:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-status/{id}", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitStatus", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "e3c4b774ba911fcda9da793f0fa095497f70f8f9", "commit_date": "2025-08-06 14:23:31 +0800", "operation": "MODIFY"}, {"method": "PUT", "path": "/edu/interactive-course-unit/update-sort", "class_name": "InteractiveCourseUnitController", "method_name": "updateInteractiveCourseUnitSort", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "e3c4b774ba911fcda9da793f0fa095497f70f8f9", "commit_date": "2025-08-06 14:23:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/interactive-course-unit/export", "class_name": "InteractiveCourseUnitController", "method_name": "exportInteractiveCourseUnit", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/admin/interactivecourse/InteractiveCourseUnitController.java", "commit_hash": "e3c4b774ba911fcda9da793f0fa095497f70f8f9", "commit_date": "2025-08-06 14:23:31 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/ai-correction", "class_name": "ExamAppController", "method_name": "aiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "46137f98c664a4d9d450fe8bad37c42823ba11be", "commit_date": "2025-08-06 16:08:24 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/category/list", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "31dabc41672d24d84ded6df70bc0367b98567a6c", "commit_date": "2025-08-11 11:21:52 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/hsk/category/list", "class_name": "AppEliteCourseController", "method_name": "getHskLevelCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "31dabc41672d24d84ded6df70bc0367b98567a6c", "commit_date": "2025-08-11 11:21:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/page", "class_name": "AppEliteCourseController", "method_name": "getEliteCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "31dabc41672d24d84ded6df70bc0367b98567a6c", "commit_date": "2025-08-11 11:21:52 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/detail/{id}", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "31dabc41672d24d84ded6df70bc0367b98567a6c", "commit_date": "2025-08-11 11:21:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/myCourse", "class_name": "AppEliteCourseController", "method_name": "getMyCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "31dabc41672d24d84ded6df70bc0367b98567a6c", "commit_date": "2025-08-11 11:21:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/getCourseChapterInfo", "class_name": "AppEliteCourseController", "method_name": "getCourseChapterInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "31dabc41672d24d84ded6df70bc0367b98567a6c", "commit_date": "2025-08-11 11:21:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/getStudyRecord", "class_name": "AppEliteCourseController", "method_name": "getStudyRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "31dabc41672d24d84ded6df70bc0367b98567a6c", "commit_date": "2025-08-11 11:21:52 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/saveStudyRecord", "class_name": "AppEliteCourseController", "method_name": "saveStudyRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "31dabc41672d24d84ded6df70bc0367b98567a6c", "commit_date": "2025-08-11 11:21:52 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/category/list", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/hsk/category/list", "class_name": "AppEliteCourseController", "method_name": "getHskLevelCategoryList", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/page", "class_name": "AppEliteCourseController", "method_name": "getEliteCoursePage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "GET", "path": "/edu/elite-course/detail/{id}", "class_name": "AppEliteCourseController", "method_name": "getEliteCourseDetail", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/myCourse", "class_name": "AppEliteCourseController", "method_name": "getMyCourse", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/getCourseChapterInfo", "class_name": "AppEliteCourseController", "method_name": "getCourseChapterInfo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/getStudyRecord", "class_name": "AppEliteCourseController", "method_name": "getStudyRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/saveStudyRecord", "class_name": "AppEliteCourseController", "method_name": "saveStudyRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/elite-course/v1/getCourseHourVideo", "class_name": "AppEliteCourseController", "method_name": "getCourseHourVideo", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/elitecourse/AppEliteCourseController.java", "commit_hash": "81f7c9b7c1c2f40ed488cfcecbc2a2e3521fada8", "commit_date": "2025-08-11 16:42:41 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/ai-correction", "class_name": "ExamAppController", "method_name": "aiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "8b047225291f26c135a0e62c54c910f26a301541", "commit_date": "2025-08-11 18:18:46 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/page", "class_name": "ExamAppController", "method_name": "getAppExamPage", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/metadata", "class_name": "ExamAppController", "method_name": "getAppExamMetadata", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/start", "class_name": "ExamAppController", "method_name": "startExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/answer-card", "class_name": "ExamAppController", "method_name": "getExamAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/report/answer-card", "class_name": "ExamAppController", "method_name": "getExamReportAnswerCard", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/subject/submit", "class_name": "ExamAppController", "method_name": "submitExamSubject", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/preview-audio", "class_name": "ExamAppController", "method_name": "getPreviewAudio", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/submit", "class_name": "ExamAppController", "method_name": "submitExam", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/sections-progress", "class_name": "ExamAppController", "method_name": "getExamSectionsProgress", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/my-exam-record", "class_name": "ExamAppController", "method_name": "getMyExamRecord", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/remaining-count", "class_name": "ExamAppController", "method_name": "getUserExamRemainingCount", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}, {"method": "POST", "path": "/edu/exam/v1/ai-correction", "class_name": "ExamAppController", "method_name": "aiCorrection", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/controller/app/exam/ExamAppController.java", "commit_hash": "****************************************", "commit_date": "2025-08-12 19:42:28 +0800", "operation": "MODIFY"}], "entities": [{"table_name": "edu_user_question_answer_record", "class_name": "UserQuestionAnswerRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerrecord/UserQuestionAnswerRecordDO.java", "fields": ["id", "userId", "questionId", "hskLevel", "textbookId", "chapterId", "unitId", "questionTypeId", "version", "practiceMode", "practiceId", "answerTime", "answerDate", "startTime", "endTime", "recordStatus", "questionNum", "correctNum", "totalScore", "userScore"], "commit_hash": "13ee06f41058037cf01a98975318061ee3ad705b", "commit_date": "2025-07-14 13:17:11 +0800", "operation": "MODIFY"}, {"table_name": "t_writing_ai_correction_record", "class_name": "WritingAiCorrectionRecordDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/writingaicorrectionrecord/WritingAiCorrectionRecordDO.java", "fields": ["id", "userId", "questionId", "questionDetailId", "questionTypeId", "questionVersion", "send<PERSON><PERSON><PERSON>", "language", "recordId", "answerDataId", "status", "correctionType", "totalScore", "errorMessage"], "commit_hash": "13ee06f41058037cf01a98975318061ee3ad705b", "commit_date": "2025-07-14 13:17:11 +0800", "operation": "MODIFY"}, {"table_name": "edu_interactive_course_unit", "class_name": "InteractiveCourseUnitDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/interactivecourse/InteractiveCourseUnitDO.java", "fields": ["id", "courseId", "unitNameCn", "unitNameEn", "unitNameOt", "displayStatus", "sort", "hskLevel", "questionSource", "videoInfoId", "coverUrl", "recommendedDuration", "resourceVersion"], "commit_hash": "2e3759fd950cfdd5f2589772cb4f8dbea4d43b36", "commit_date": "2025-07-14 13:32:35 +0800", "operation": "MODIFY"}, {"table_name": "game_special_exercise", "class_name": "SpecialExerciseDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/specialexercise/SpecialExerciseDO.java", "fields": ["id", "nameCn", "nameEn", "nameOt", "type", "hskLevel", "difficultyLevel", "isShow", "sort", "relatedVersion", "source"], "commit_hash": "6de0a104cee65944f20a7f4d5d6412074f124ec3", "commit_date": "2025-07-14 19:25:38 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "progress", "practiceStatus", "isNewest", "source"], "commit_hash": "b457a3157c3cf02848f0f2dad9a052effbcf0a59", "commit_date": "2025-07-15 10:25:25 +0800", "operation": "MODIFY"}, {"table_name": "game_special_exercise", "class_name": "SpecialExerciseDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/specialexercise/SpecialExerciseDO.java", "fields": ["id", "nameCn", "nameEn", "nameOt", "type", "hskLevel", "difficultyLevel", "isShow", "sort", "relatedVersion"], "commit_hash": "b457a3157c3cf02848f0f2dad9a052effbcf0a59", "commit_date": "2025-07-15 10:25:25 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "progress", "practiceStatus", "isNewest", "source"], "commit_hash": "991768ab7a9164dc62c53a0c3917fc952f71f1dd", "commit_date": "2025-07-15 15:20:30 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "progress", "practiceStatus", "isNewest", "source", "exerciseQuestionIds"], "commit_hash": "a8240a149087ba43be5422060eb5b3fd29d59318", "commit_date": "2025-07-15 15:35:18 +0800", "operation": "MODIFY"}, {"table_name": "game_record_details", "class_name": "GameRecordDetailsDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordDetailsDO.java", "fields": ["id", "userId", "specialExerciseId", "questionId", "exerciseQuestionId", "exerciseQuestionVersionId", "recordSummaryId", "type", "questionContent", "referenceAnswer", "userAnswer", "isCorrect", "answerTime", "answerDate", "answerReport", "version", "answerSequence", "score", "answerStatus"], "commit_hash": "468b0c663b3cb5990e261eea56580b0c276f4200", "commit_date": "2025-07-15 17:43:53 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "progress", "practiceStatus", "isNewest", "source"], "commit_hash": "f09ee0b5cc088655c294fa5a065195555a383045", "commit_date": "2025-07-15 17:48:06 +0800", "operation": "MODIFY"}, {"table_name": "t_writing_ai_correction_detail", "class_name": "WritingAiCorrectionDetailDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/writingaicorrectiondetail/WritingAiCorrectionDetailDO.java", "fields": ["id", "recordId", "dimensionType", "score", "status", "totalScore", "content", "originalResult", "analysisResult"], "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"table_name": "t_writing_ai_correction_record", "class_name": "WritingAiCorrectionRecordDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/writingaicorrectionrecord/WritingAiCorrectionRecordDO.java", "fields": ["id", "userId", "questionId", "questionDetailId", "questionTypeId", "questionVersion", "send<PERSON><PERSON><PERSON>", "language", "recordId", "answerDataId", "status", "correctionType", "totalScore", "score", "errorMessage"], "commit_hash": "3d57947cf3fd0fc4d119f7bc8e0f9e29e95612aa", "commit_date": "2025-07-15 17:49:43 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "answerDate", "progress", "practiceStatus", "isNewest", "source"], "commit_hash": "8f3834d582ceb681f8f2ec21585e6139b95b10e4", "commit_date": "2025-07-15 19:14:53 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_exam_record", "class_name": "UserExamRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamRecordDO.java", "fields": ["id", "userId", "examId", "hskLevel", "examType", "examSections", "totalScore", "actualScore", "listeningScore", "readingScore", "writingScore", "questionNum", "answerNum", "correctNum", "wrongNum", "unansweredNum", "answerTime", "startTime", "endTime", "recordStatus", "isNewest", "status", "progress"], "commit_hash": "3a21e3c465c3523553a54e05a1c4fa0d78373813", "commit_date": "2025-07-16 16:29:35 +0800", "operation": "MODIFY"}, {"table_name": "t_writing_ai_correction_record", "class_name": "WritingAiCorrectionRecordDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/writingaicorrectionrecord/WritingAiCorrectionRecordDO.java", "fields": ["id", "userId", "questionId", "questionDetailId", "questionTypeId", "questionVersion", "send<PERSON><PERSON><PERSON>", "language", "recordId", "answerDataId", "status", "correctionType", "totalScore", "score", "errorMessage"], "commit_hash": "5f976e4c957e63590afb5a50d686ef1e7849ac59", "commit_date": "2025-07-16 17:13:52 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "answerDate", "progress", "practiceStatus", "isNewest", "source", "questionVersion"], "commit_hash": "c63765bc853e0aa7402db9f72aa5c5743ab15a9d", "commit_date": "2025-07-17 11:50:49 +0800", "operation": "MODIFY"}, {"table_name": "t_ai_correction_times", "class_name": "AiCorrectionTimesDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/aicorrectiontimes/AiCorrectionTimesDO.java", "fields": ["id", "userId", "bizType", "bizId", "callTime", "callCount", "originId"], "commit_hash": "45a1850d06f06af8a67c8c2bfd69477dc465f4db", "commit_date": "2025-07-17 15:30:49 +0800", "operation": "ADD"}, {"table_name": "edu_user_interactive_course_record", "class_name": "UserInteractiveCourseRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/interactivecourse/UserInteractiveCourseRecordDO.java", "fields": ["id", "userId", "courseId", "unitId", "bizType", "bizId", "isLatest", "videoDuration", "viewingDuration", "videoProgress", "status", "aiCorrectionStatus", "accuracy", "resourceVersion", "gameId", "questionIds", "saveOption"], "commit_hash": "67ef5dcf701faa20b432447d9a3bc9403e453987", "commit_date": "2025-07-17 18:57:28 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_exam_record", "class_name": "UserExamRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamRecordDO.java", "fields": ["id", "userId", "examId", "hskLevel", "examType", "examSections", "totalScore", "actualScore", "listeningScore", "readingScore", "writingScore", "questionNum", "answerNum", "correctNum", "wrongNum", "unansweredNum", "answerTime", "startTime", "endTime", "recordStatus", "isNewest", "practiceStatus", "progress", "listeningRemainingTime", "readingRemainingTime", "writingRemainingTime"], "commit_hash": "71e35fb454344d63c3a033237a267b3e20fd0693", "commit_date": "2025-07-17 19:45:26 +0800", "operation": "MODIFY"}, {"table_name": "game_record_details", "class_name": "GameRecordDetailsDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordDetailsDO.java", "fields": ["id", "userId", "specialExerciseId", "questionId", "exerciseQuestionId", "exerciseQuestionVersionId", "recordSummaryId", "type", "questionContent", "referenceAnswer", "userAnswer", "pinyin", "isCorrect", "answerTime", "answerDate", "answerReport", "version", "answerSequence", "score", "answerStatus"], "commit_hash": "cd29d2e51cda0768ddf066987987d7d8fcce168f", "commit_date": "2025-07-18 15:16:06 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "answerDate", "progress", "practiceStatus", "isNewest", "source", "questionVersion", "exerciseQuestionVersionIds"], "commit_hash": "cd29d2e51cda0768ddf066987987d7d8fcce168f", "commit_date": "2025-07-18 15:16:06 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_practice_record", "class_name": "UserPracticeRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userpracticerecord/UserPracticeRecordDO.java", "fields": ["id", "userId", "hskLevel", "textbookId", "chapterId", "subject", "unitSort", "questionTypeId", "questionNum", "answerNum", "correctNum", "answerTime", "startTime", "endTime", "recordStatus", "isNewest", "questionIds", "practiceMode"], "commit_hash": "df54bd5441158801084ef3d6f1301f455c7c0d02", "commit_date": "2025-07-18 17:44:36 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "answerDate", "progress", "practiceStatus", "isNewest", "source", "questionVersion", "exerciseQuestionVersionIds", "accuracy", "averageScore"], "commit_hash": "6be45e3791b2aae7ee337673afee717da70fee64", "commit_date": "2025-07-19 15:36:47 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_exam_record", "class_name": "UserExamRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamRecordDO.java", "fields": ["id", "userId", "examId", "hskLevel", "examType", "examSections", "totalScore", "actualScore", "listeningScore", "readingScore", "writingScore", "questionNum", "answerNum", "correctNum", "wrongNum", "unansweredNum", "answerTime", "startTime", "endTime", "correctionStatus", "isNewest", "practiceStatus", "progress", "listeningRemainingTime", "readingRemainingTime", "writingRemainingTime"], "commit_hash": "a7fed65a7fcc7fb851e1f796695ed98fcb6edd7c", "commit_date": "2025-07-20 17:32:19 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_exam_record", "class_name": "UserExamRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamRecordDO.java", "fields": ["id", "userId", "examId", "hskLevel", "examType", "examSections", "totalScore", "actualScore", "listeningScore", "readingScore", "writingScore", "questionNum", "answerNum", "correctNum", "wrongNum", "unansweredNum", "answerTime", "startTime", "endTime", "correctionStatus", "isNewest", "practiceStatus", "progress", "listeningRemainingTime", "readingRemainingTime", "writingRemainingTime"], "commit_hash": "d88783c214eff51d33f1b56493088bf95f0a6dd3", "commit_date": "2025-07-20 23:02:51 +0800", "operation": "MODIFY"}, {"table_name": "edu_interactive_course_unit", "class_name": "InteractiveCourseUnitDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/interactivecourse/InteractiveCourseUnitDO.java", "fields": ["id", "courseId", "unitNameCn", "unitNameEn", "unitNameOt", "displayStatus", "sort", "hskLevel", "questionSource", "videoInfoId", "coverUrl", "recommendedDuration", "resourceVersion"], "commit_hash": "2a109334d101633508e9d8183b903f6c3bb07054", "commit_date": "2025-07-21 14:48:41 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_favorite", "class_name": "UserFavoriteDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/favorite/UserFavoriteDO.java", "fields": ["id", "userId", "favoriteType", "favoriteSource", "targetId", "isWrongQuestion", "favoriteTime", "cancelTime"], "commit_hash": "44021fe052ad9b49b730cdeb65e7e42252860c86", "commit_date": "2025-07-22 12:16:40 +0800", "operation": "ADD"}, {"table_name": "t_ai_correction_times", "class_name": "AiCorrectionTimesDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/aicorrectiontimes/AiCorrectionTimesDO.java", "fields": ["id", "userId", "bizType", "bizId", "callTime", "callCount", "originId", "recordId"], "commit_hash": "d88b223eae123faaf320ad3b1e954ff753173550", "commit_date": "2025-07-22 14:50:58 +0800", "operation": "MODIFY"}, {"table_name": "user_ext", "class_name": "UserExtDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/ext/UserExtDO.java", "fields": ["id", "studyTime"], "commit_hash": "38585163f415b44a910ba8c152b77c3b019f7232", "commit_date": "2025-07-22 16:42:12 +0800", "operation": "ADD"}, {"table_name": "user_ext", "class_name": "UserExtDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/ext/UserExtDO.java", "fields": ["id", "studyTime"], "commit_hash": "7fdb2f74d5b744778651dcbbfca269d3bea9c6ed", "commit_date": "2025-07-23 11:33:26 +0800", "operation": "MODIFY"}, {"table_name": "edu_question", "class_name": "QuestionDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/question/QuestionDO.java", "fields": ["id", "hskLevel", "textbookId", "chapterId", "unitId", "typeId", "subject", "materialAudio", "materialAudioContent", "materialImage", "materialContent", "options", "questionNum", "status", "isShow", "version", "questionCode", "correctAnswerCount", "totalAnswerCount"], "commit_hash": "521f61763c67cd2bde73e7ad75c2e0528dc62f69", "commit_date": "2025-07-23 14:01:06 +0800", "operation": "MODIFY"}, {"table_name": "user_favorite", "class_name": "UserFavoriteDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/favorite/UserFavoriteDO.java", "fields": ["id", "userId", "favoriteType", "favoriteSource", "targetId", "gameId", "isWrongQuestion", "favoriteTime", "cancelTime"], "commit_hash": "cc118c598fe463f7fc882cb8de51f526565022ff", "commit_date": "2025-07-23 15:27:24 +0800", "operation": "MODIFY"}, {"table_name": "game_question", "class_name": "GameQuestionDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/question/GameQuestionDO.java", "fields": ["id", "hskLevel", "type", "questionContent", "questionSplit", "pinyin", "audioUrl", "knowledgePoint", "knowledgePointPinyin", "referenceAnswer", "isShow", "latestVersion", "referenceCount"], "commit_hash": "618db7a021feaa363de04b6cf06524eb9a22f931", "commit_date": "2025-07-23 19:14:08 +0800", "operation": "MODIFY"}, {"table_name": "game_question_version", "class_name": "GameQuestionVersionDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/question/GameQuestionVersionDO.java", "fields": ["id", "hskLevel", "type", "questionId", "questionContent", "questionSplit", "pinyin", "audioUrl", "knowledgePoint", "knowledgePointPinyin", "referenceAnswer", "version", "isShow"], "commit_hash": "618db7a021feaa363de04b6cf06524eb9a22f931", "commit_date": "2025-07-23 19:14:08 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_question_answer_data", "class_name": "UserQuestionAnswerDataDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerdata/UserQuestionAnswerDataDO.java", "fields": ["id", "recordId", "userAnswer", "answer", "isCorrect", "aiCorrectStatus", "questionId", "questionDetailId", "version", "questionDetailVersionId"], "commit_hash": "36f72401ba263df093d7ec04b259412cd0e9687c", "commit_date": "2025-07-23 20:16:02 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_question_answer_record", "class_name": "UserQuestionAnswerRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerrecord/UserQuestionAnswerRecordDO.java", "fields": ["id", "userId", "questionId", "hskLevel", "textbookId", "chapterId", "unitId", "questionTypeId", "version", "questionVersionId", "practiceMode", "practiceId", "answerTime", "answerDate", "startTime", "endTime", "recordStatus", "questionNum", "correctNum", "totalScore", "userScore"], "commit_hash": "36f72401ba263df093d7ec04b259412cd0e9687c", "commit_date": "2025-07-23 20:16:02 +0800", "operation": "MODIFY"}, {"table_name": "user_favorite", "class_name": "UserFavoriteDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/favorite/UserFavoriteDO.java", "fields": ["id", "userId", "favoriteType", "favoriteSource", "targetId", "targetDetailId", "gameId", "isWrongQuestion", "favoriteTime", "cancelTime"], "commit_hash": "36f72401ba263df093d7ec04b259412cd0e9687c", "commit_date": "2025-07-23 20:16:02 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "answerDate", "progress", "practiceStatus", "isNewest", "source", "questionVersion", "exerciseQuestionVersionIds", "accuracy", "averageScore", "currentRound"], "commit_hash": "****************************************", "commit_date": "2025-07-24 15:41:57 +0800", "operation": "MODIFY"}, {"table_name": "t_ai_correction_times", "class_name": "AiCorrectionTimesDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/aicorrectiontimes/AiCorrectionTimesDO.java", "fields": ["id", "userId", "bizType", "bizId", "callTime", "callCount", "originId", "recordId"], "commit_hash": "8bdda71f4d7662f473498d3292da405ec8e8ccd8", "commit_date": "2025-07-24 17:50:50 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_practice_record", "class_name": "UserPracticeRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userpracticerecord/UserPracticeRecordDO.java", "fields": ["id", "userId", "hskLevel", "textbookId", "chapterId", "subject", "unitSort", "questionTypeId", "questionNum", "answerNum", "correctNum", "answerTime", "startTime", "endTime", "recordStatus", "isNewest", "questionIds", "interactiveCourseUnitId", "practiceMode"], "commit_hash": "fc27faba8882054ab0565174c16c62d86da85eb5", "commit_date": "2025-07-25 13:20:59 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "answerDate", "progress", "practiceStatus", "interactiveCourseUnitId", "isNewest", "source", "questionVersion", "exerciseQuestionVersionIds", "accuracy", "averageScore"], "commit_hash": "fc27faba8882054ab0565174c16c62d86da85eb5", "commit_date": "2025-07-25 13:20:59 +0800", "operation": "MODIFY"}, {"table_name": "game_record_summary", "class_name": "GameRecordSummaryDO", "file_path": "hsk-module-game/hsk-module-game-biz/src/main/java/com/xt/hsk/module/game/dal/dataobject/record/GameRecordSummaryDO.java", "fields": ["id", "userId", "specialExerciseId", "type", "totalQuestions", "answeredQuestions", "correctQuestions", "wrongQuestions", "unansweredQuestions", "answerStartTime", "answerEndTime", "answerDate", "progress", "practiceStatus", "interactiveCourseUnitId", "isNewest", "source", "questionVersion", "exerciseQuestionVersionIds", "accuracy", "averageScore", "currentRound"], "commit_hash": "865b8eca10a0a8e4aa813d43a10e5f44ddd54db1", "commit_date": "2025-07-25 13:21:26 +0800", "operation": "MODIFY"}, {"table_name": "edu_question_detail", "class_name": "QuestionDetailDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/question/questiondetail/QuestionDetailDO.java", "fields": ["id", "questionId", "attachmentAudio", "attachmentAudioContent", "attachmentAudioTime", "attachmentImage", "attachmentContent", "answer", "options", "version", "explainTextCn", "explainTextEn", "explainTextOt", "explainAudio", "explainVideo", "sort", "creator"], "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"table_name": "edu_question_detail_version", "class_name": "QuestionDetailVersionDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/question/questiondetailversion/QuestionDetailVersionDO.java", "fields": ["id", "questionId", "questionDetailId", "attachmentAudio", "attachmentAudioContent", "attachmentAudioTime", "attachmentImage", "attachmentContent", "answer", "options", "version", "explainTextCn", "explainTextEn", "explainTextOt", "explainAudio", "explainVideo", "sort", "creator"], "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"table_name": "edu_question_type", "class_name": "QuestionTypeDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/question/questiontype/QuestionTypeDO.java", "fields": ["id", "nameCn", "nameEn", "nameOt", "subject", "hskLevel"], "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_question_answer_data", "class_name": "UserQuestionAnswerDataDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerdata/UserQuestionAnswerDataDO.java", "fields": ["id", "recordId", "userAnswer", "answer", "isCorrect", "aiCorrectStatus", "questionId", "questionDetailId", "version", "questionDetailVersionId"], "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_question_answer_record", "class_name": "UserQuestionAnswerRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerrecord/UserQuestionAnswerRecordDO.java", "fields": ["id", "userId", "questionId", "hskLevel", "textbookId", "chapterId", "unitId", "questionTypeId", "version", "questionVersionId", "practiceMode", "practiceId", "answerTime", "answerDate", "startTime", "endTime", "recordStatus", "questionNum", "correctNum", "totalScore", "userScore"], "commit_hash": "aff472781fbcca8e5ce8464d6413cb9294a95060", "commit_date": "2025-07-25 13:26:00 +0800", "operation": "MODIFY"}, {"table_name": "edu_exam_detail_version", "class_name": "ExamDetailVersionDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/ExamDetailVersionDO.java", "fields": ["id", "examId", "examDetailId", "subject", "unit", "examQuestionTypeId", "questionTypeIds", "questionNames", "version", "questionCount", "questions"], "commit_hash": "538c4fc7eb2581e37767f208277f28b021b04e54", "commit_date": "2025-07-27 01:17:16 +0800", "operation": "ADD"}, {"table_name": "edu_exam", "class_name": "ExamDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/ExamDO.java", "fields": ["id", "hskLevel", "paperRuleId", "name", "type", "coverUrl", "description", "listeningDuration", "readingDuration", "writingDuration", "sort", "examCount", "totalScore", "publishStatus", "examDetailVersion"], "commit_hash": "1d714a830003f2541ba8c584c538b50f648af573", "commit_date": "2025-07-27 01:44:15 +0800", "operation": "MODIFY"}, {"table_name": "edu_exam_detail", "class_name": "ExamDetailDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/ExamDetailDO.java", "fields": ["id", "examId", "subject", "unit", "examQuestionTypeId", "questionTypeIds", "questionNames", "questionCount", "questions", "version"], "commit_hash": "1d714a830003f2541ba8c584c538b50f648af573", "commit_date": "2025-07-27 01:44:15 +0800", "operation": "MODIFY"}, {"table_name": "edu_question_detail", "class_name": "QuestionDetailDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/question/questiondetail/QuestionDetailDO.java", "fields": ["id", "questionId", "attachmentAudio", "attachmentAudioContent", "attachmentAudioTime", "attachmentImage", "attachmentImageDesc", "attachmentContent", "answer", "options", "version", "explainTextCn", "explainTextEn", "explainTextOt", "explainAudio", "explainVideo", "sort", "creator"], "commit_hash": "9697391782abef853546b0597e13f4586a19f83e", "commit_date": "2025-07-28 13:47:28 +0800", "operation": "MODIFY"}, {"table_name": "edu_question_detail_version", "class_name": "QuestionDetailVersionDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/question/questiondetailversion/QuestionDetailVersionDO.java", "fields": ["id", "questionId", "questionDetailId", "attachmentAudio", "attachmentAudioContent", "attachmentAudioTime", "attachmentImage", "attachmentImageDesc", "attachmentContent", "answer", "options", "version", "explainTextCn", "explainTextEn", "explainTextOt", "explainAudio", "explainVideo", "sort", "creator"], "commit_hash": "9697391782abef853546b0597e13f4586a19f83e", "commit_date": "2025-07-28 13:47:28 +0800", "operation": "MODIFY"}, {"table_name": "user_change_mobile_record", "class_name": "ChangeMobileRecordDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/changemobile/ChangeMobileRecordDO.java", "fields": ["id", "userId", "sessionId", "state", "oldCountryCode", "oldMobile", "oldMobileVerified", "oldMobileVerifiedTime", "newCountryCode", "newMobile", "newMobileCodeSent", "newMobileCodeSentTime", "retryCount", "clientIp", "userAgent", "completedTime", "failedReason", "failedTime"], "commit_hash": "b14a9c72f99a77dfe83619bd0d8f67177eb0a266", "commit_date": "2025-07-28 19:41:23 +0800", "operation": "ADD"}, {"table_name": "trade_order_item", "class_name": "OrderItemDO", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/dal/dataobject/order/OrderItemDO.java", "fields": ["id", "userId", "tradeOrderId", "tradeOrderNo", "currency", "productId", "productType", "productName", "productPrice", "quantity", "totalAmount", "discountAmount", "payAmount", "couponReductionAmount", "afterSaleStatus", "refundAmount", "afterSaleOrderNo"], "commit_hash": "fd1fb104ebc539e783c0ad299781cd16b7e0e7eb", "commit_date": "2025-07-29 11:25:37 +0800", "operation": "MODIFY"}, {"table_name": "trade_order_item", "class_name": "OrderItemDO", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/dal/dataobject/order/OrderItemDO.java", "fields": ["id", "userId", "tradeOrderId", "tradeOrderNo", "currency", "productId", "productType", "productNameCn", "productNameEn", "productNameOt", "productPrice", "quantity", "totalAmount", "discountAmount", "payAmount", "couponReductionAmount", "afterSaleStatus", "refundAmount", "afterSaleOrderNo"], "commit_hash": "c1dfab0ae8b187591c28038efdd6347ae5fc7568", "commit_date": "2025-07-29 11:46:20 +0800", "operation": "MODIFY"}, {"table_name": "edu_elite_course_register", "class_name": "EliteCourseRegisterDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/elitecourse/EliteCourseRegisterDO.java", "fields": ["id", "userId", "courseId", "courseType", "enrollmentTime", "registerType", "orderNumber", "orderDetailId", "remarks", "courseRegisterStatus", "beginTime", "endTime", "learningValidityPeriod"], "commit_hash": "b914831c13189df803c3c8dc60cdecad04106a26", "commit_date": "2025-07-29 14:42:37 +0800", "operation": "MODIFY"}, {"table_name": "course_and_register_dto", "class_name": "CourseAndRegisterDto", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/manager/elitecourse/dto/CourseAndRegisterDto.java", "fields": ["primaryCategoryId", "secondaryCategoryId", "courseNameCn", "courseNameEn", "courseNameOt", "coverUrlLarge", "coverUrlSmall", "classHourNumberStatus", "customClassHourNumber", "originalPriceCn", "sellingPriceCn", "originalPriceEn", "sellingPriceEn", "originalPriceOt", "sellingPriceOt", "courseDetail", "listingMethod", "listingStatus", "listingTime", "salesBase", "deadline", "effectiveDays", "effectModel", "sort", "isShow", "enrollmentCount", "type"], "commit_hash": "b914831c13189df803c3c8dc60cdecad04106a26", "commit_date": "2025-07-29 14:42:37 +0800", "operation": "ADD"}, {"table_name": "trade_order_remark", "class_name": "OrderRemarkDO", "file_path": "hsk-module-trade/hsk-module-trade-biz/src/main/java/com/xt/hsk/module/trade/dal/dataobject/order/OrderRemarkDO.java", "fields": ["id", "tradeOrderId", "isLatest", "content"], "commit_hash": "ee132f8d3916b215b4278a76ec8874f830c5d0a1", "commit_date": "2025-07-30 10:44:53 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_exam_unit_score", "class_name": "UserExamUnitScoreDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamUnitScoreDO.java", "fields": ["id", "userId", "examId", "examRecordId", "subject", "unit", "score"], "commit_hash": "16059fb678376a280fc298411042575169710747", "commit_date": "2025-07-30 16:11:39 +0800", "operation": "ADD"}, {"table_name": "edu_user_question_answer_record", "class_name": "UserQuestionAnswerRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerrecord/UserQuestionAnswerRecordDO.java", "fields": ["id", "userId", "questionId", "hskLevel", "textbookId", "chapterId", "unitId", "subject", "questionTypeId", "version", "questionVersionId", "practiceMode", "practiceId", "answerTime", "answerDate", "startTime", "endTime", "recordStatus", "questionNum", "correctNum", "totalScore", "userScore"], "commit_hash": "2eb4b0009bd4540841bc8920e04bf4fd0104e4c9", "commit_date": "2025-07-30 17:08:03 +0800", "operation": "MODIFY"}, {"table_name": "user", "class_name": "UserDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/user/UserDO.java", "fields": ["id", "nickname", "avatar", "mobile", "countryCode", "country", "currentHskLevel", "gender", "birthDate", "examDate", "password", "lastLoginTime", "lastLoginIp", "passwordChanged", "infoUpdated", "profession", "learningPurpose", "chineseLevel", "targetHskLevel", "plannedExamDate", "userSource", "status", "deleteTime"], "commit_hash": "f895a9b954116bcc1f09b5762e8e3cdbe970af9c", "commit_date": "2025-07-30 17:35:56 +0800", "operation": "MODIFY"}, {"table_name": "user_change_mobile_record", "class_name": "ChangeMobileRecordDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/changemobile/ChangeMobileRecordDO.java", "fields": ["id", "userId", "sessionId", "state", "oldCountryCode", "oldMobile", "oldMobileVerified", "oldMobileVerifiedTime", "newCountryCode", "newMobile", "newMobileCodeSent", "newMobileCodeSentTime", "retryCount", "clientIp", "userAgent", "completedTime", "failedReason", "failedTime"], "commit_hash": "806b7a66745304c8bf303b2ba9a6741652171c5a", "commit_date": "2025-07-30 18:41:02 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_question_answer_record", "class_name": "UserQuestionAnswerRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerrecord/UserQuestionAnswerRecordDO.java", "fields": ["id", "userId", "questionId", "hskLevel", "textbookId", "chapterId", "unitId", "subject", "questionTypeId", "version", "questionVersionId", "practiceMode", "practiceId", "answerTime", "answerDate", "startTime", "endTime", "recordStatus", "questionNum", "correctNum", "totalScore", "userScore", "unitSort"], "commit_hash": "aa732cfce4106d319f4d284e26594f11a50bf365", "commit_date": "2025-08-01 10:48:48 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_question_answer_record", "class_name": "UserQuestionAnswerRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerrecord/UserQuestionAnswerRecordDO.java", "fields": ["id", "userId", "questionId", "hskLevel", "textbookId", "chapterId", "unitId", "subject", "questionTypeId", "version", "questionVersionId", "practiceMode", "practiceId", "answerTime", "answerDate", "startTime", "endTime", "recordStatus", "questionNum", "correctNum", "totalScore", "userScore", "unitSort"], "commit_hash": "fdabdc1d96bc169716de9fbd873e7f9eab4ffe76", "commit_date": "2025-08-01 10:49:21 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_exam_record", "class_name": "UserExamRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamRecordDO.java", "fields": ["id", "userId", "examId", "hskLevel", "examType", "examSections", "totalScore", "actualScore", "listeningScore", "readingScore", "writingScore", "questionNum", "answerNum", "correctNum", "wrongNum", "unansweredNum", "answerTime", "startTime", "endTime", "correctionStatus", "isNewest", "practiceStatus", "progress", "listeningRemainingTime", "readingRemainingTime", "writingRemainingTime", "examDetailVersion"], "commit_hash": "2c76f66b51cae9d87a04cdc45bcd8570c2edb26e", "commit_date": "2025-08-01 13:09:56 +0800", "operation": "MODIFY"}, {"table_name": "user", "class_name": "UserDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/user/UserDO.java", "fields": ["id", "nickname", "avatar", "mobile", "countryCode", "country", "currentHskLevel", "gender", "birthDate", "examDate", "password", "lastLoginTime", "lastLoginIp", "passwordChanged", "infoUpdated", "profession", "learningPurpose", "chineseLevel", "targetHskLevel", "plannedExamDate", "userSource", "status", "deleteTime"], "commit_hash": "6325cca4998cf348c7fbc837b43fd018ad18fa9c", "commit_date": "2025-08-01 14:16:00 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_self_assessment_record", "class_name": "UserSelfAssessmentRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userselfassessmentrecord/UserSelfAssessmentRecordDO.java", "fields": ["id", "userId", "answerNum", "correctNum", "questionNum", "startTime", "endTime", "recordStatus", "questionIds", "predictionLevel"], "commit_hash": "53568336028e2696295485334f360db8867dc0d4", "commit_date": "2025-08-01 14:22:07 +0800", "operation": "ADD"}, {"table_name": "edu_user_self_assessment_record", "class_name": "UserSelfAssessmentRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userselfassessmentrecord/UserSelfAssessmentRecordDO.java", "fields": ["id", "userId", "answerNum", "correctNum", "questionNum", "startTime", "endTime", "recordStatus", "questionIds", "predictionLevel"], "commit_hash": "26763bfba17e19c8e54202ee73489e4d2255c57a", "commit_date": "2025-08-01 18:27:00 +0800", "operation": "MODIFY"}, {"table_name": "user_change_mobile_record", "class_name": "ChangeMobileRecordDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/changemobile/ChangeMobileRecordDO.java", "fields": ["id", "userId", "sessionId", "state", "oldCountryCode", "oldMobile", "oldMobileVerified", "oldMobileVerifiedTime", "newCountryCode", "newMobile", "newMobileCodeSent", "newMobileCodeSentTime", "retryCount", "clientIp", "userAgent", "completedTime", "failedReason", "failedTime"], "commit_hash": "ceb279b1417c099063fbe9e3f6a09b105562b543", "commit_date": "2025-08-02 10:14:44 +0800", "operation": "MODIFY"}, {"table_name": "t_video", "class_name": "VideoDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/video/VideoDO.java", "fields": ["id", "assetId", "coverUrl", "playType", "videoUrl", "quality", "codec", "duration", "durationMs", "videoSize", "width", "hight", "platform", "source", "videoId"], "commit_hash": "21ba08fa688cdf7c2b0011fbc9d82905da9baf3c", "commit_date": "2025-08-02 11:14:11 +0800", "operation": "ADD"}, {"table_name": "edu_user_exam_record", "class_name": "UserExamRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamRecordDO.java", "fields": ["id", "userId", "examId", "hskLevel", "examType", "examSections", "currentSections", "totalScore", "actualScore", "listeningScore", "readingScore", "writingScore", "questionNum", "answerNum", "correctNum", "wrongNum", "unansweredNum", "answerTime", "startTime", "endTime", "correctionStatus", "isNewest", "practiceStatus", "progress", "listeningRemainingTime", "readingRemainingTime", "writingRemainingTime", "examDetailVersion"], "commit_hash": "9c1f2ac8df8bbeed0dcfc247b0bea3083e66ed8d", "commit_date": "2025-08-03 19:03:02 +0800", "operation": "MODIFY"}, {"table_name": "t_ai_correction_times", "class_name": "AiCorrectionTimesDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/aicorrectiontimes/AiCorrectionTimesDO.java", "fields": ["id", "userId", "bizType", "bizId", "thirdType", "callTime", "callCount", "originId", "recordId"], "commit_hash": "e8554bc3b1b4930673605a4c3302f8415301d9e6", "commit_date": "2025-08-04 10:38:15 +0800", "operation": "MODIFY"}, {"table_name": "edu_interactive_course_unit", "class_name": "InteractiveCourseUnitDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/interactivecourse/InteractiveCourseUnitDO.java", "fields": ["id", "courseId", "unitNameCn", "unitNameEn", "unitNameOt", "displayStatus", "sort", "hskLevel", "questionSource", "videoId", "videoType", "aspectRatio", "videoNameCn", "videoNameEn", "videoNameOt", "coverUrl", "recommendedDuration", "resourceVersion"], "commit_hash": "f576a9b8af1e7196850475f03c049d6828687861", "commit_date": "2025-08-04 11:02:37 +0800", "operation": "MODIFY"}, {"table_name": "edu_interactive_course_video_info", "class_name": "InteractiveCourseVideoInfoDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/interactivecourse/InteractiveCourseVideoInfoDO.java", "fields": ["id", "unitId", "videoNameCn", "videoNameEn", "videoNameOt", "videoLinks"], "commit_hash": "f576a9b8af1e7196850475f03c049d6828687861", "commit_date": "2025-08-04 11:02:37 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_question_answer_record", "class_name": "UserQuestionAnswerRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerrecord/UserQuestionAnswerRecordDO.java", "fields": ["id", "userId", "questionId", "hskLevel", "textbookId", "chapterId", "unitId", "subject", "questionTypeId", "version", "questionVersionId", "practiceMode", "practiceId", "answerTime", "answerDate", "startTime", "endTime", "recordStatus", "questionNum", "correctNum", "totalScore", "userScore", "unitSort"], "commit_hash": "5808291720cbee8b83b2c0dbe8f816969b28676a", "commit_date": "2025-08-04 13:37:14 +0800", "operation": "MODIFY"}, {"table_name": "t_ai_correction_times", "class_name": "AiCorrectionTimesDO", "file_path": "hsk-module-thirdparty/hsk-module-thirdparty-biz/src/main/java/com/xt/hsk/module/thirdparty/dal/dataobject/aicorrectiontimes/AiCorrectionTimesDO.java", "fields": ["id", "userId", "bizType", "bizId", "thirdType", "callTime", "callCount", "originId", "recordId"], "commit_hash": "5808291720cbee8b83b2c0dbe8f816969b28676a", "commit_date": "2025-08-04 13:37:14 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_exam_unit_score", "class_name": "UserExamUnitScoreDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamUnitScoreDO.java", "fields": ["id", "userId", "examId", "examRecordId", "subject", "unit", "score"], "commit_hash": "209c1ccc0d69bb353749191f1dc0fe806de9bfc7", "commit_date": "2025-08-05 13:27:17 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_question_answer_record", "class_name": "UserQuestionAnswerRecordDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/userquestionanswerrecord/UserQuestionAnswerRecordDO.java", "fields": ["id", "userId", "questionId", "hskLevel", "textbookId", "chapterId", "unitId", "subject", "questionTypeId", "version", "questionVersionId", "practiceMode", "practiceId", "answerTime", "answerDate", "startTime", "endTime", "recordStatus", "questionNum", "correctNum", "totalScore", "userScore", "unitSort"], "commit_hash": "2183f3a43b6ce780ca4ddcdc945287173ab8829a", "commit_date": "2025-08-05 16:57:36 +0800", "operation": "MODIFY"}, {"table_name": "edu_user_exam_answer_progress", "class_name": "UserExamAnswerProgressDO", "file_path": "hsk-module-edu/hsk-module-edu-biz/src/main/java/com/xt/hsk/module/edu/dal/dataobject/exam/UserExamAnswerProgressDO.java", "fields": ["id", "userId", "questionId", "questionDetailId", "questionType", "progress", "examRecordId", "playType", "subject"], "commit_hash": "b20224055ba9310a5e60b915474f5ef3938c4fe9", "commit_date": "2025-08-05 22:28:51 +0800", "operation": "ADD"}, {"table_name": "user", "class_name": "UserDO", "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/dal/dataobject/user/UserDO.java", "fields": ["id", "nickname", "avatar", "mobile", "countryCode", "country", "currentHskLevel", "gender", "birthDate", "examDate", "password", "lastLoginTime", "lastLoginIp", "passwordChanged", "infoUpdated", "profession", "learningPurpose", "chineseLevel", "targetHskLevel", "plannedExamDate", "userSource", "status", "deleteTime"], "commit_hash": "a00ac5b392bacabfc18ff2881e2504874347afa9", "commit_date": "2025-08-06 17:32:39 +0800", "operation": "MODIFY"}]}