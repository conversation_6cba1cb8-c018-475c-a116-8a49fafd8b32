# HSK项目Git分析报告

**生成时间**: 2025-08-13 10:25:23

## 📊 总体统计

| 类型 | 总数 | 新增 | 修改 | 删除 |
|------|------|------|------|------|
| 🔗 HTTP接口 | 915 | 9 | 906 | 0 |
| 🗃️ 数据库实体 | 80 | 10 | 70 | 0 |

## 🆕 新增接口详情

| HTTP方法 | 接口路径 | 控制器类 | 方法名 | 提交信息 |
|----------|----------|----------|--------|----------|
| `POST` | `/home/<USER>
| `POST` | `/edu/exam/v1/page` | `ExamAppController` | `getAppExamPage` | `3a21e3c4` (2025-07-16) |
| `POST` | `/game/correction/v1/remaining-count` | `GameCorrectionAppController` | `getUserRemainingCount` | `c9d82987` (2025-07-21) |
| `POST` | `/user/favorite/gameQuestionsList` | `UserFavoriteAppController` | `gameQuestionsList` | `cc118c59` (2025-07-23) |
| `POST` | `/game/favorite/word/status` | `GameFavoriteAppController` | `checkWordFavoriteStatus` | `f3c4d6ac` (2025-07-23) |
| `POST` | `/third-party/you-dao/text-to-speech` | `YouDaoAdminController` | `textToSpeech` | `567f5fff` (2025-07-24) |
| `POST` | `/edu/user-self-assessment-record/start` | `UserSelfAssessmentRecordController` | `createUserSelfAssessmentRecord` | `53568336` (2025-08-01) |
| `PUT` | `/edu/user-self-assessment-record/submit` | `UserSelfAssessmentRecordController` | `submitUserSelfAssessmentRecord` | `53568336` (2025-08-01) |
| `POST` | `/edu/user-exam-record/report` | `UserExamRecordAppController` | `getExamReport` | `4b75b3d2` (2025-08-05) |

## 🆕 新增数据库实体详情

| 表名 | 实体类名 | 字段列表 | 提交信息 |
|------|----------|----------|----------|
| `t_ai_correction_times` | `AiCorrectionTimesDO` | id, userId, bizType, bizId, callTime ... (共7个字段) | `45a1850d` (2025-07-17) |
| `edu_user_favorite` | `UserFavoriteDO` | id, userId, favoriteType, favoriteSource, targetId ... (共8个字段) | `44021fe0` (2025-07-22) |
| `user_ext` | `UserExtDO` | id, studyTime | `38585163` (2025-07-22) |
| `edu_exam_detail_version` | `ExamDetailVersionDO` | id, examId, examDetailId, subject, unit ... (共11个字段) | `538c4fc7` (2025-07-27) |
| `user_change_mobile_record` | `ChangeMobileRecordDO` | id, userId, sessionId, state, oldCountryCode ... (共18个字段) | `b14a9c72` (2025-07-28) |
| `course_and_register_dto` | `CourseAndRegisterDto` | primaryCategoryId, secondaryCategoryId, courseNameCn, courseNameEn, courseNameOt ... (共27个字段) | `b914831c` (2025-07-29) |
| `edu_user_exam_unit_score` | `UserExamUnitScoreDO` | id, userId, examId, examRecordId, subject ... (共7个字段) | `16059fb6` (2025-07-30) |
| `edu_user_self_assessment_record` | `UserSelfAssessmentRecordDO` | id, userId, answerNum, correctNum, questionNum ... (共10个字段) | `53568336` (2025-08-01) |
| `t_video` | `VideoDO` | id, assetId, coverUrl, playType, videoUrl ... (共15个字段) | `21ba08fa` (2025-08-02) |
| `edu_user_exam_answer_progress` | `UserExamAnswerProgressDO` | id, userId, questionId, questionDetailId, questionType ... (共9个字段) | `b2022405` (2025-08-05) |

## 📋 技术说明

### 分析范围
- **HTTP接口**: 识别Spring Boot项目中的`@RestController`、`@Controller`注解的类
- **HTTP方法**: 支持`@GetMapping`、`@PostMapping`、`@PutMapping`、`@DeleteMapping`、`@PatchMapping`等注解
- **数据库实体**: 识别MyBatis Plus项目中的`@Entity`、`@Table`、`@TableName`注解的类

---

*本报告由HSK项目Git分析工具自动生成*