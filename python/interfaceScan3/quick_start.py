#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
简化的Git接口和实体分析工具启动器
"""

import os
import sys
from datetime import datetime, timedelta
from git_interface_analyzer import GitInterfaceAnalyzer

def main():
    print("="*60)
    print("🚀 Java Spring项目Git分析工具 - 快速启动")
    print("="*60)
    
    # 获取项目路径
    print("\n📁 请输入你的Java Spring项目路径:")
    print("   (例如: D:\\code\\your-project 或直接回车使用默认路径)")
    
    project_path = input("项目路径: ").strip()
    
    # 使用默认路径
    if not project_path:
        project_path = r"D:\code\Hsk-java"
        print(f"使用默认路径: {project_path}")
    
    # 检查路径是否存在
    if not os.path.exists(project_path):
        print(f"❌ 错误: 项目路径不存在: {project_path}")
        return
    
    git_path = os.path.join(project_path, '.git')
    if not os.path.exists(git_path):
        print(f"❌ 错误: 指定路径不是Git仓库: {project_path}")
        return
    
    print(f"✅ 项目路径验证成功: {project_path}")
    
    # 选择分析模式
    print("\n📊 请选择分析模式:")
    print("   1. 最近7天")
    print("   2. 最近30天")
    print("   3. 自定义时间范围")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        # 最近7天
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
    elif choice == "2":
        # 最近30天
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
    elif choice == "3":
        # 自定义时间范围
        print("\n📅 请输入时间范围 (格式: YYYY-MM-DD):")
        start_str = input("开始日期: ").strip()
        end_str = input("结束日期: ").strip()
        
        # 验证日期格式
        try:
            datetime.strptime(start_str, '%Y-%m-%d')
            datetime.strptime(end_str, '%Y-%m-%d')
        except ValueError:
            print("❌ 错误: 日期格式不正确，请使用 YYYY-MM-DD 格式")
            return
    else:
        print("❌ 错误: 无效的选择")
        return
    
    print(f"\n🔍 开始分析项目: {project_path}")
    print(f"📅 时间范围: {start_str} 到 {end_str}")
    print("-" * 50)
    
    try:
        # 创建分析器
        analyzer = GitInterfaceAnalyzer(project_path)
        
        # 执行分析
        print("⏳ 正在分析Git记录...")
        report = analyzer.analyze_period(start_str, end_str)
        
        # 生成输出文件名
        project_name = os.path.basename(project_path)
        output_file = f"{project_name}_analysis_{start_str}_to_{end_str}.json"
        
        # 保存报告
        analyzer.save_report(report, output_file)
        
        # 打印结果
        print_analysis_report(report, output_file)
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        print("请检查项目路径和Git仓库状态")

def print_analysis_report(report, output_file):
    """打印分析报告"""
    summary = report['summary']
    
    print("\n" + "="*60)
    print("🎯 分析报告")
    print("="*60)
    
    print(f"\n📊 总体统计:")
    print(f"  📡 接口总数: {summary['total_interfaces']}")
    print(f"  🗃️  实体总数: {summary['total_entities']}")
    
    print(f"\n🆕 新增统计:")
    print(f"  ➕ 新增接口: {summary['new_interfaces']}")
    print(f"  ➕ 新增实体: {summary['new_entities']}")
    
    print(f"\n🔄 修改统计:")
    print(f"  🔄 修改接口: {summary['modified_interfaces']}")
    print(f"  🔄 修改实体: {summary['modified_entities']}")
    
    print(f"\n🗑️ 删除统计:")
    print(f"  ❌ 删除接口: {summary['deleted_interfaces']}")
    print(f"  ❌ 删除实体: {summary['deleted_entities']}")
    
    # 显示部分接口详情
    interfaces = report.get('interfaces', [])
    if interfaces:
        print(f"\n🔗 接口详情 (显示前5个):")
        for i, interface in enumerate(interfaces[:5]):
            print(f"  🌟 {interface['method']} {interface['path']}")
            print(f"     📁 {interface['class_name']}.{interface['method_name']}")
            print(f"     📄 {interface['file_path']}")
            print(f"     🔗 提交: {interface['commit_hash'][:8]} ({interface['commit_date'][:10]})")
            print(f"     🏷️  操作: {interface['operation']}")
            if i < len(interfaces[:5]) - 1:
                print()
    
    # 显示部分实体详情
    entities = report.get('entities', [])
    if entities:
        print(f"\n🗃️ 实体详情 (显示前3个):")
        for i, entity in enumerate(entities[:3]):
            print(f"  🌟 表: {entity['table_name']} (类: {entity['class_name']})")
            if entity['fields']:
                fields_str = ', '.join(entity['fields'][:5])
                if len(entity['fields']) > 5:
                    fields_str += f" ... (共{len(entity['fields'])}个字段)"
                print(f"     📋 字段: {fields_str}")
            print(f"     📄 {entity['file_path']}")
            print(f"     🔗 提交: {entity['commit_hash'][:8]} ({entity['commit_date'][:10]})")
            print(f"     🏷️  操作: {entity['operation']}")
            if i < len(entities[:3]) - 1:
                print()
    
    print(f"\n💾 详细报告已保存到: {output_file}")
    print(f"\n✅ 分析完成!")
    
    # 询问是否打开报告文件
    print(f"\n❓ 是否要打开详细报告文件? (y/n): ", end="")
    open_file = input().strip().lower()
    if open_file in ['y', 'yes', '是']:
        try:
            os.startfile(output_file)
        except:
            print(f"无法自动打开文件，请手动打开: {output_file}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
    
    input("\n按回车键退出...")