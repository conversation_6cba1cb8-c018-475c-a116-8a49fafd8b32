# HSK项目Git分析工具

这是一个专门用于分析Java Spring项目Git记录的Python工具，可以统计指定时间段内新增的HTTP接口和数据库实体。

## 功能特性

- 🔍 **接口分析**: 自动识别Spring Boot项目中的HTTP接口
  - 支持 `@RestController`, `@Controller` 注解
  - 识别 `@GetMapping`, `@PostMapping`, `@PutMapping`, `@DeleteMapping` 等注解
  - 提取接口路径、HTTP方法、所在类和方法名

- 🗃️ **实体分析**: 自动识别MyBatis Plus实体类
  - 支持 `@Entity`, `@Table`, `@TableName` 注解
  - 提取表名、字段信息
  - 自动转换驼峰命名到下划线命名

- 📊 **变更统计**: 统计指定时间段内的变更
  - 新增、修改、删除的接口数量
  - 新增、修改、删除的实体数量
  - 详细的变更记录和提交信息

## 文件说明

- `git_interface_analyzer.py`: 核心分析器类，包含所有分析逻辑
- `quick_start.py`: 快速启动脚本，最简单的使用方式
- `启动分析工具.bat`: Windows一键启动批处理文件
- `hsk_project_analyzer.py`: HSK项目专用分析器
- `example_usage.py`: 通用使用示例
- `使用指南.md`: 详细的使用指南和配置说明
- `README.md`: 功能说明和使用文档

## 快速开始

### 1. 环境要求

- Python 3.7+
- Git命令行工具
- Java Spring项目（支持任何使用Spring框架的项目）

### 2. 运行分析器

#### 方法一：一键启动（最简单）

**Windows用户**：双击运行 `启动分析工具.bat`

**或者命令行运行**：
```bash
cd D:\extends\python\interfaceScan2
python quick_start.py
```

这是最简单的方式，会引导你输入项目路径和选择分析时间范围。

#### 方法二：使用HSK专用分析器

```bash
cd D:\extends\python\interfaceScan2
python hsk_project_analyzer.py
```

然后按照菜单提示选择分析模式：
- 快速分析（最近7天）
- 周分析（最近7天）
- 月分析（最近30天）
- 自定义时间范围

#### 方法三：使用通用分析器

```bash
cd D:\extends\python\interfaceScan2
python example_usage.py
```

#### 方法四：命令行直接使用

```bash
cd D:\extends\python\interfaceScan2
python git_interface_analyzer.py --repo "你的项目路径" --start "2024-07-01" --end "2024-08-12" --output "report.json"
```

### 3. 查看结果

分析完成后会生成：
- 控制台输出：摘要统计和详细信息
- JSON报告文件：完整的分析数据

## 输出示例

```
🎯 HSK项目分析报告
============================================================

📊 总体统计:
  📡 接口总数: 15
  🗃️  实体总数: 8

🆕 新增统计:
  ➕ 新增接口: 5
  ➕ 新增实体: 2

🔗 新增接口详情:
  🌟 POST /api/user/register
     📁 UserController.register
     📄 src/main/java/com/hsk/controller/UserController.java
     🔗 提交: a1b2c3d4 (2024-08-10)

🗃️ 新增实体详情:
  🌟 表: user_profile (类: UserProfile)
     📋 字段: id, user_id, nickname, avatar, created_time
     📄 src/main/java/com/hsk/entity/UserProfile.java
     🔗 提交: e5f6g7h8 (2024-08-11)
```

## 配置说明

### 修改项目路径

如果你的HSK项目不在 `D:\code\Hsk-java`，请修改 `hsk_project_analyzer.py` 中的路径：

```python
# HSK项目配置
HSK_PROJECT_PATH = r"你的项目路径"
```

### 自定义识别规则

可以在 `git_interface_analyzer.py` 中修改识别规则：

```python
# Spring注解模式
self.controller_patterns = [
    r'@RestController',
    r'@Controller',
    r'@RequestMapping'
]

# MyBatis Plus实体注解模式
self.entity_patterns = [
    r'@Entity',
    r'@Table',
    r'@TableName'
]
```

## 常见问题

### Q: 提示"项目路径不存在"
A: 请确认HSK项目路径是否正确，或修改 `HSK_PROJECT_PATH` 变量

### Q: 提示"不是Git仓库"
A: 请确认指定路径是Git仓库，包含 `.git` 文件夹

### Q: 没有识别到接口或实体
A: 请检查代码是否使用了标准的Spring Boot和MyBatis Plus注解

### Q: 中文乱码问题
A: 确保终端支持UTF-8编码，或使用支持中文的IDE运行

## 技术原理

1. **Git分析**: 使用Git命令获取指定时间范围内的提交记录
2. **文件解析**: 分析每个提交中变更的Java文件
3. **注解识别**: 使用正则表达式识别Spring和MyBatis Plus注解
4. **信息提取**: 提取接口路径、HTTP方法、实体表名、字段等信息
5. **报告生成**: 生成统计报告和详细的JSON数据

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多Spring注解
- 分析接口参数和返回值
- 生成HTML格式报告
- 集成到CI/CD流程
- 发送邮件通知

## 许可证

本工具仅供学习和内部使用。
