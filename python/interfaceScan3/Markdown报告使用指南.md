# Markdown报告使用指南

## 🎯 功能概述

HSK项目Git分析工具现在支持自动生成**Markdown格式**的分析报告，让你可以更直观地查看和分享分析结果。

## 📋 报告格式

生成的Markdown报告包含以下内容：

### 1. 📊 总体统计表格
- HTTP接口和数据库实体的总数、新增、修改、删除统计
- 清晰的表格格式展示

### 2. 🆕 新增接口详情
- HTTP方法、接口路径、控制器类、方法名
- 对应的Git提交信息

### 3. 🆕 新增实体详情  
- 表名、实体类名、字段列表
- 对应的Git提交信息

### 4. 📋 技术说明
- 分析范围和识别规则说明
- 变更类型定义

## 🚀 使用方法

### 方法一：自动生成（推荐）

运行任何分析器时，会**自动同时生成JSON和Markdown两种格式**的报告：

```bash
# 运行HSK专用分析器
python hsk_project_analyzer.py

# 运行快速分析器  
python quick_analyzer.py

# 运行通用分析器
python git_interface_analyzer.py --repo "D:\code\Hsk-java" --start "2024-07-01" --end "2024-08-12"
```

**输出文件：**
- `hsk_analysis_2024-07-01_to_2024-08-12.json` （JSON格式）
- `hsk_analysis_2024-07-01_to_2024-08-12.md` （Markdown格式）

### 方法二：手动转换现有JSON报告

如果你已经有JSON报告文件，可以单独转换为Markdown：

```bash
python report_to_markdown.py
```

程序会自动找到JSON报告文件并转换。

## 📄 报告示例

### 总体统计
| 类型 | 总数 | 新增 | 修改 | 删除 |
|------|------|------|------|------|
| 🔗 HTTP接口 | 15 | 5 | 8 | 2 |
| 🗃️ 数据库实体 | 8 | 2 | 4 | 1 |

### 新增接口详情
| HTTP方法 | 接口路径 | 控制器类 | 方法名 | 提交信息 |
|----------|----------|----------|--------|----------|
| `POST` | `/edu/elite-course/v1/getCourseHourVideo` | `AppEliteCourseController` | `getCourseHourVideo` | `81f7c9b7` (2025-08-11) |
| `GET` | `/api/user/profile` | `UserController` | `getUserProfile` | `a1b2c3d4` (2025-08-10) |

### 新增实体详情
| 表名 | 实体类名 | 字段列表 | 提交信息 |
|------|----------|----------|----------|
| `user_profile` | `UserProfile` | id, user_id, nickname, avatar, created_time | `e5f6g7h8` (2025-08-11) |

## 🔧 自定义配置

### 修改报告模板

如果需要自定义Markdown报告格式，可以修改 `git_interface_analyzer.py` 中的 `generate_markdown_report` 方法。

### 字段显示控制

- **字段数量限制**：默认只显示前5个字段，超过会显示"..."
- **提交哈希长度**：默认显示前8位
- **日期格式**：默认显示YYYY-MM-DD格式

## 📱 查看和分享

### 本地查看
- 使用任何支持Markdown的编辑器（如VS Code、Typora、Obsidian等）
- 在GitHub、GitLab等平台上直接预览

### 团队分享
- 可以直接复制Markdown内容到团队协作工具
- 支持导出为PDF或HTML格式
- 可以嵌入到项目文档中

## 🎨 报告优势

### 相比JSON格式：
- ✅ **可读性更强**：表格化展示，一目了然
- ✅ **易于分享**：可以直接粘贴到文档或聊天工具
- ✅ **支持预览**：在GitHub等平台可以直接查看
- ✅ **格式美观**：支持表格、代码块、emoji等

### 相比纯文本：
- ✅ **结构化展示**：清晰的章节和表格
- ✅ **语法高亮**：代码和路径有特殊格式
- ✅ **交互性**：在支持的平台可以点击链接

## 🔍 故障排除

### 中文显示问题
如果在某些环境下中文显示异常，请确保：
- 终端支持UTF-8编码
- 使用支持中文的Markdown查看器

### 文件生成失败
如果Markdown文件生成失败：
- 检查磁盘空间是否充足
- 确认有文件写入权限
- 查看控制台错误信息

### 表格格式问题
如果表格显示不正常：
- 确保使用支持Markdown表格的查看器
- 检查是否有特殊字符影响格式

## 📞 技术支持

如果遇到问题或需要新功能：
1. 检查控制台输出的错误信息
2. 确认JSON报告是否正常生成
3. 尝试重新运行分析器

---

*本指南适用于HSK项目Git分析工具 v1.0+*
