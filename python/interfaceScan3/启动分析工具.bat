@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo ========================================
echo    Java Spring项目Git分析工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 运行快速启动脚本
echo 🚀 启动分析工具...
echo.
python quick_start.py

echo.
echo 👋 程序已结束
pause