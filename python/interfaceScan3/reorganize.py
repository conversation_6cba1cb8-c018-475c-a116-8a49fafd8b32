#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新组织项目文件结构
"""

import os
import shutil
from pathlib import Path

def reorganize_files():
    """重新组织文件"""
    base_dir = Path(__file__).parent
    
    # 文件移动映射
    file_moves = {
        # 移动到 tests/debug/
        "debug_interface.py": "tests/debug/debug_interface.py",
        "test_specific_commit.py": "tests/debug/test_specific_commit.py",
        
        # 移动到 tests/
        "test_analyzer.py": "tests/test_analyzer.py",
        "test_date.py": "tests/test_date.py",
        
        # 移动到 scripts/
        "hsk_project_analyzer.py": "scripts/run_hsk_analyzer.py",
        "quick_analyzer.py": "scripts/run_quick_analyzer.py",
        "generate_sample_report.py": "scripts/generate_sample_report.py",
        "report_to_markdown.py": "scripts/report_converter.py",
        
        # 移动到 scripts/batch/
        "run_hsk_analyzer.bat": "scripts/batch/run_hsk_analyzer.bat",
        "启动分析工具.bat": "scripts/batch/启动分析工具.bat",
        
        # 移动到 docs/
        "README.md": "docs/使用说明.md",
        "Markdown报告使用指南.md": "docs/Markdown报告使用指南.md",
        "使用指南.md": "docs/详细使用指南.md",
        
        # 移动到 output/
        "hsk_analysis_2025-07-14_to_2025-08-13.json": "output/hsk_analysis_2025-07-14_to_2025-08-13.json",
        "hsk_analysis_2025-07-14_to_2025-08-13.md": "output/hsk_analysis_2025-07-14_to_2025-08-13.md",
    }
    
    # 创建必要的目录
    directories = [
        "tests/debug",
        "scripts/batch",
        "docs",
        "output"
    ]
    
    for directory in directories:
        dir_path = base_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    # 移动文件
    moved_count = 0
    for source, target in file_moves.items():
        source_path = base_dir / source
        target_path = base_dir / target
        
        if source_path.exists():
            try:
                # 如果目标文件已存在，先删除
                if target_path.exists():
                    target_path.unlink()
                
                # 移动文件
                shutil.move(str(source_path), str(target_path))
                print(f"✅ 移动: {source} -> {target}")
                moved_count += 1
            except Exception as e:
                print(f"❌ 移动失败: {source} -> {target}, 错误: {e}")
        else:
            print(f"⚠️  文件不存在: {source}")
    
    print(f"\n📊 移动完成，共移动 {moved_count} 个文件")
    
    # 创建 .gitkeep 文件
    gitkeep_dirs = ["output", "tests/debug"]
    for directory in gitkeep_dirs:
        gitkeep_path = base_dir / directory / ".gitkeep"
        if not gitkeep_path.exists():
            gitkeep_path.touch()
            print(f"✅ 创建 .gitkeep: {directory}")

def create_missing_files():
    """创建缺失的文件"""
    base_dir = Path(__file__).parent
    
    # 创建 tests/__init__.py
    tests_init = base_dir / "tests" / "__init__.py"
    if not tests_init.exists():
        tests_init.write_text('"""测试模块"""', encoding='utf-8')
        print("✅ 创建: tests/__init__.py")
    
    # 创建 scripts/__init__.py
    scripts_init = base_dir / "scripts" / "__init__.py"
    if not scripts_init.exists():
        scripts_init.write_text('"""启动脚本模块"""', encoding='utf-8')
        print("✅ 创建: scripts/__init__.py")

def cleanup_old_files():
    """清理旧文件"""
    base_dir = Path(__file__).parent
    
    # 可以删除的旧文件
    old_files = [
        "example_usage.py",  # 已被新的脚本替代
        "quick_start.py",    # 已被main.py替代
    ]
    
    for old_file in old_files:
        old_path = base_dir / old_file
        if old_path.exists():
            try:
                old_path.unlink()
                print(f"🗑️  删除旧文件: {old_file}")
            except Exception as e:
                print(f"❌ 删除失败: {old_file}, 错误: {e}")

def print_new_structure():
    """打印新的目录结构"""
    print("\n📁 新的项目结构:")
    print("""
interfaceScan2/
├── 📁 core/                    # 核心分析引擎
├── 📁 analyzers/               # 不同类型的分析器
├── 📁 reports/                 # 报告生成模块
├── 📁 utils/                   # 工具函数
├── 📁 tests/                   # 测试文件
│   └── 📁 debug/               # 调试工具
├── 📁 scripts/                 # 启动脚本
│   └── 📁 batch/               # 批处理文件
├── 📁 docs/                    # 文档
├── 📁 output/                  # 输出目录
├── 📁 config/                  # 配置文件
├── requirements.txt
└── main.py                     # 主入口文件
    """)

def main():
    """主函数"""
    print("🔄 重新组织项目文件结构")
    print("="*50)
    
    try:
        # 重新组织文件
        reorganize_files()
        
        # 创建缺失的文件
        create_missing_files()
        
        # 清理旧文件
        cleanup_old_files()
        
        # 打印新结构
        print_new_structure()
        
        print("\n✅ 项目重新组织完成!")
        print("\n📋 使用建议:")
        print("1. 使用 python main.py 作为主入口")
        print("2. 脚本文件在 scripts/ 目录下")
        print("3. 文档在 docs/ 目录下")
        print("4. 输出文件在 output/ 目录下")
        print("5. 测试文件在 tests/ 目录下")
        
    except Exception as e:
        print(f"❌ 重新组织过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
