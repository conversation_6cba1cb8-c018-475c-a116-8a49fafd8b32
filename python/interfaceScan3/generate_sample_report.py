#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成示例报告 - 演示Markdown报告功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from git_interface_analyzer import GitInterfaceAnalyzer

def generate_sample_report():
    """生成示例报告"""
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    
    print("📝 生成示例报告")
    print("="*50)
    
    if not os.path.exists(HSK_PROJECT_PATH):
        print(f"❌ 项目路径不存在: {HSK_PROJECT_PATH}")
        return
    
    # 创建分析器
    analyzer = GitInterfaceAnalyzer(HSK_PROJECT_PATH)
    
    print("🔍 分析最近3天的变化...")
    
    # 分析最近3天
    from datetime import datetime, timedelta
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    print(f"📅 时间范围: {start_str} 到 {end_str}")
    
    # 执行分析
    report = analyzer.analyze_period(start_str, end_str)
    
    # 保存报告（会自动生成JSON和Markdown两种格式）
    output_file = f"sample_report_{start_str}_to_{end_str}.json"
    analyzer.save_report(report, output_file)
    
    # 打印摘要
    summary = report['summary']
    print(f"\n📊 分析结果摘要:")
    print(f"  📡 总接口数: {summary['total_interfaces']}")
    print(f"  ➕ 新增接口: {summary['new_interfaces']}")
    print(f"  ✏️ 修改接口: {summary['modified_interfaces']}")
    print(f"  🗃️ 总实体数: {summary['total_entities']}")
    print(f"  ➕ 新增实体: {summary['new_entities']}")
    print(f"  ✏️ 修改实体: {summary['modified_entities']}")
    
    print(f"\n✅ 报告生成完成!")
    print(f"📄 JSON报告: {output_file}")
    print(f"📝 Markdown报告: {output_file.replace('.json', '.md')}")

if __name__ == "__main__":
    generate_sample_report()
