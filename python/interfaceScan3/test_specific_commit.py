#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定提交的接口识别
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from git_interface_analyzer import GitInterfaceAnaly<PERSON>

def test_specific_commit():
    """测试特定提交"""
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    target_commit = "81f7c9b7"
    
    print("🎯 测试特定提交的接口识别")
    print("="*60)
    print(f"项目路径: {HSK_PROJECT_PATH}")
    print(f"目标提交: {target_commit}")
    
    # 创建分析器
    analyzer = GitInterfaceAnalyzer(HSK_PROJECT_PATH)
    
    # 获取提交信息
    print(f"\n📋 获取提交信息...")
    commit_info_cmd = ['git', 'show', '--format=%H|%ci|%s', '--no-patch', target_commit]
    commit_info = analyzer.run_git_command(commit_info_cmd)
    
    if commit_info:
        parts = commit_info.strip().split('|')
        if len(parts) >= 3:
            commit_hash, commit_date, commit_msg = parts[0], parts[1], parts[2]
            print(f"  提交哈希: {commit_hash}")
            print(f"  提交时间: {commit_date}")
            print(f"  提交信息: {commit_msg}")
        else:
            print(f"  提交信息: {commit_info}")
    
    # 获取变更的文件
    print(f"\n📁 获取变更的文件...")
    changed_files = analyzer.get_changed_files(target_commit)
    
    print(f"  找到 {len(changed_files)} 个Java文件变更:")
    for status, file_path in changed_files:
        print(f"    {status} {file_path}")
    
    # 分析每个文件
    print(f"\n🔍 分析文件内容...")
    total_interfaces = 0
    total_entities = 0
    
    for status, file_path in changed_files:
        print(f"\n  📄 分析文件: {file_path}")
        print(f"     状态: {status}")
        
        if status != "D":  # 不是删除的文件
            # 获取文件内容
            content = analyzer.get_file_content_at_commit(target_commit, file_path)
            
            if content:
                print(f"     ✅ 成功获取文件内容 ({len(content)} 字符)")
                
                # 提取接口
                interfaces = analyzer.extract_interfaces_from_content(
                    content, file_path, target_commit, commit_date, "ADD" if status == "A" else "MODIFY"
                )
                
                # 提取实体
                entities = analyzer.extract_entities_from_content(
                    content, file_path, target_commit, commit_date, "ADD" if status == "A" else "MODIFY"
                )
                
                print(f"     📡 找到接口: {len(interfaces)} 个")
                print(f"     🗃️  找到实体: {len(entities)} 个")
                
                # 显示接口详情
                if interfaces:
                    for i, interface in enumerate(interfaces, 1):
                        print(f"       {i}. {interface.method} {interface.path}")
                        print(f"          类: {interface.class_name}.{interface.method_name}")
                        
                        # 检查是否是目标接口
                        if 'getCourseHourVideo' in interface.path or 'getCourseHourVideo' in interface.method_name:
                            print(f"          🎯 这是目标接口!")
                
                # 显示实体详情
                if entities:
                    for i, entity in enumerate(entities, 1):
                        print(f"       {i}. 表: {entity.table_name} (类: {entity.class_name})")
                
                total_interfaces += len(interfaces)
                total_entities += len(entities)
            else:
                print(f"     ❌ 无法获取文件内容")
        else:
            print(f"     ⚠️  文件已删除，跳过分析")
    
    print(f"\n📊 分析结果总结:")
    print(f"  总接口数: {total_interfaces}")
    print(f"  总实体数: {total_entities}")
    
    # 检查目标接口是否被找到
    target_found = False
    if total_interfaces > 0:
        print(f"\n🔍 检查目标接口是否被识别...")
        # 这里我们已经在上面的循环中检查过了
        print(f"  如果上面显示了 '🎯 这是目标接口!'，说明已被识别")
        print(f"  如果没有显示，说明识别逻辑有问题")

def test_time_range():
    """测试时间范围是否包含目标提交"""
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    target_commit = "81f7c9b7"
    
    print(f"\n⏰ 测试时间范围")
    print("="*40)
    
    # 创建分析器
    analyzer = GitInterfaceAnalyzer(HSK_PROJECT_PATH)
    
    # 测试不同的时间范围
    time_ranges = [
        ("2025-08-11", "2025-08-12", "单天范围"),
        ("2025-08-10", "2025-08-13", "3天范围"),
        ("2025-08-01", "2025-08-31", "整月范围"),
        ("2025-07-01", "2025-08-31", "两月范围")
    ]
    
    for start_date, end_date, desc in time_ranges:
        print(f"\n📅 测试 {desc}: {start_date} 到 {end_date}")
        
        commits = analyzer.get_commits_in_range(start_date, end_date)
        
        # 检查目标提交是否在范围内
        target_in_range = False
        for commit_hash, commit_date in commits:
            if commit_hash.startswith(target_commit[:8]):
                target_in_range = True
                print(f"  ✅ 找到目标提交: {commit_hash[:8]} ({commit_date})")
                break
        
        if not target_in_range:
            print(f"  ❌ 目标提交不在此时间范围内")
        
        print(f"  📊 总提交数: {len(commits)}")

def main():
    """主函数"""
    print("🧪 特定提交测试工具")
    print("="*60)
    
    # 测试特定提交
    test_specific_commit()
    
    # 测试时间范围
    test_time_range()
    
    print(f"\n✅ 测试完成")
    print(f"如果目标接口被识别但没有出现在最终统计中，")
    print(f"可能是时间范围或分支选择的问题。")

if __name__ == "__main__":
    main()
