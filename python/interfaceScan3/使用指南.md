# Java Spring项目Git分析工具使用指南

## 工具概述

这是一个专门用于分析Java Spring项目Git记录的Python工具，可以统计指定时间段内新增、修改、删除的HTTP接口和数据库实体。

## 核心功能

### 🔍 HTTP接口分析
- 自动识别Spring Boot控制器类（`@RestController`, `@Controller`）
- 提取HTTP映射注解（`@GetMapping`, `@PostMapping`, `@PutMapping`, `@DeleteMapping`, `@RequestMapping`）
- 统计接口的增删改情况
- 记录接口路径、HTTP方法、所在类和方法名

### 🗃️ 数据库实体分析
- 识别JPA/MyBatis Plus实体类（`@Entity`, `@Table`, `@TableName`）
- 提取表名和字段信息
- 自动转换驼峰命名到下划线命名
- 统计实体的增删改情况

### 📊 变更统计
- 按时间段统计变更数量
- 生成详细的变更记录
- 输出JSON格式的分析报告
- 提供美观的控制台输出

## 快速使用

### 方法一：HSK项目专用分析器（推荐）

```bash
cd d:\extends\python\interfaceScan2
python hsk_project_analyzer.py
```

然后选择分析模式：
- `1` - 快速分析（最近7天）
- `2` - 周分析（最近7天）
- `3` - 月分析（最近30天）
- `4` - 自定义时间范围

### 方法二：通用分析器

```bash
cd d:\extends\python\interfaceScan2
python example_usage.py
```

### 方法三：命令行直接使用

```bash
cd d:\extends\python\interfaceScan2
python git_interface_analyzer.py --repo "你的项目路径" --start "2024-07-01" --end "2024-08-12" --output "report.json"
```

## 配置说明

### 修改项目路径

如果你的项目不在默认路径，需要修改以下文件中的路径配置：

**hsk_project_analyzer.py**:
```python
HSK_PROJECT_PATH = r"D:\code\Hsk-java"  # 修改为你的项目路径
```

**example_usage.py**:
```python
repo_path = r"D:\code\Hsk-java"  # 修改为你的项目路径
```

### 自定义时间范围

在代码中修改日期范围：
```python
start_date = "2024-07-01"  # 开始日期
end_date = "2024-08-12"    # 结束日期
```

## 输出示例

### 控制台输出
```
🎯 HSK项目分析报告
============================================================

📊 总体统计:
  📡 接口总数: 17
  🗃️  实体总数: 80

🆕 新增统计:
  ➕ 新增接口: 0
  ➕ 新增实体: 10

🔄 修改统计:
  🔄 修改接口: 17
  🔄 修改实体: 70

🗑️ 删除统计:
  ❌ 删除接口: 0
  ❌ 删除实体: 0

🔗 接口详情:
  🌟 POST /user/import/template
     📁 AppUserAdminController.downloadImportTemplate
     📄 hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java
     🔗 提交: 198ba461 (2025-07-18 18:33:33)
```

### JSON报告格式
```json
{
  "summary": {
    "total_interfaces": 17,
    "total_entities": 80,
    "new_interfaces": 0,
    "new_entities": 10,
    "modified_interfaces": 17,
    "modified_entities": 70,
    "deleted_interfaces": 0,
    "deleted_entities": 0
  },
  "interfaces": [
    {
      "method": "POST",
      "path": "/user/import/template",
      "class_name": "AppUserAdminController",
      "method_name": "downloadImportTemplate",
      "file_path": "hsk-module-user/hsk-module-user-biz/src/main/java/com/xt/hsk/module/user/controller/admin/user/AppUserAdminController.java",
      "commit_hash": "198ba461a1ed80d515e7404485ba524698a02a2e",
      "commit_date": "2025-07-18 18:33:33 +0800",
      "operation": "MODIFY"
    }
  ],
  "entities": [
    {
      "table_name": "user_profile",
      "class_name": "UserProfile",
      "file_path": "src/main/java/com/example/entity/UserProfile.java",
      "fields": ["id", "user_id", "nickname", "avatar"],
      "commit_hash": "e5f6g7h8",
      "commit_date": "2024-08-11",
      "operation": "ADD"
    }
  ]
}
```

## 文件说明

- **git_interface_analyzer.py**: 核心分析器类，包含所有分析逻辑
- **hsk_project_analyzer.py**: HSK项目专用分析器，提供交互式菜单
- **example_usage.py**: 通用使用示例，可以作为模板修改
- **README.md**: 详细的功能说明和使用文档
- **使用指南.md**: 本文件，快速上手指南

## 注意事项

1. **Git仓库要求**: 确保项目是一个有效的Git仓库
2. **Python版本**: 需要Python 3.7或更高版本
3. **Git命令**: 确保系统已安装Git命令行工具
4. **路径格式**: Windows系统使用反斜杠路径分隔符
5. **权限问题**: 确保对项目目录有读取权限

## 常见问题

### Q: 提示"项目路径不存在"怎么办？
A: 检查并修改代码中的项目路径配置，确保路径正确且存在。

### Q: 提示"不是Git仓库"怎么办？
A: 确保指定的路径是一个Git仓库，包含.git文件夹。

### Q: 没有检测到接口或实体怎么办？
A: 检查项目是否使用了标准的Spring注解，工具会自动识别常见的注解模式。

### Q: 如何分析其他Java项目？
A: 修改配置文件中的项目路径，工具支持所有使用Spring框架和MyBatis Plus的Java项目。

## 扩展使用

### 批量分析多个项目
可以修改脚本来循环分析多个项目：

```python
projects = [
    r"D:\code\project1",
    r"D:\code\project2",
    r"D:\code\project3"
]

for project_path in projects:
    analyzer = GitInterfaceAnalyzer(project_path)
    report = analyzer.analyze_period(start_date, end_date)
    analyzer.save_report(report, f"{project_path.split('\\')[-1]}_report.json")
```

### 定期自动分析
可以结合Windows任务计划程序或cron定期运行分析：

```bash
# 每周一运行分析
python hsk_project_analyzer.py
```

这个工具可以帮助你快速了解项目在特定时间段内的接口和数据库变更情况，非常适合用于代码审查、版本发布前的变更统计等场景。