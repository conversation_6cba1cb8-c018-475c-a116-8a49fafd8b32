#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSK项目专用分析器
专门用于分析D:\\code\\Hsk-java项目的Git记录
"""

import os
import sys
from datetime import datetime, timedelta
from git_interface_analyzer import GitInterfaceAnalyzer

# HSK项目配置
HSK_PROJECT_PATH = r"D:\code\Hsk-java"

def check_project_exists():
    """检查项目路径是否存在"""
    if not os.path.exists(HSK_PROJECT_PATH):
        print(f"❌ 错误: 项目路径不存在: {HSK_PROJECT_PATH}")
        print("请确认路径是否正确，或者修改HSK_PROJECT_PATH变量")
        return False

    git_path = os.path.join(HSK_PROJECT_PATH, '.git')
    if not os.path.exists(git_path):
        print(f"❌ 错误: 指定路径不是Git仓库: {HSK_PROJECT_PATH}")
        return False

    print(f"✅ 项目路径验证成功: {HSK_PROJECT_PATH}")
    return True

def get_available_branches():
    """获取可用的分支列表"""
    try:
        import subprocess
        result = subprocess.run(
            ['git', 'branch', '-a'],
            cwd=HSK_PROJECT_PATH,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=10
        )

        if result.returncode == 0:
            branches = []
            for line in result.stdout.strip().split('\n'):
                line = line.strip()
                if line and not line.startswith('*'):
                    # 移除 origin/ 前缀
                    if line.startswith('remotes/origin/'):
                        branch = line.replace('remotes/origin/', '')
                        if branch != 'HEAD':
                            branches.append(branch)
                    elif not line.startswith('remotes/'):
                        branches.append(line)
                elif line.startswith('* '):
                    # 当前分支
                    current = line[2:]
                    branches.insert(0, f"{current} (当前)")

            return list(set(branches))  # 去重
        else:
            print(f"❌ 获取分支列表失败: {result.stderr}")
            return []
    except Exception as e:
        print(f"❌ 获取分支列表异常: {e}")
        return []

def select_branch():
    """选择分支"""
    branches = get_available_branches()

    if not branches:
        print("❌ 无法获取分支列表，将使用当前分支")
        return None

    print("\n🌿 可用分支:")
    print("0. 当前分支")
    for i, branch in enumerate(branches, 1):
        print(f"{i}. {branch}")

    while True:
        try:
            choice = input(f"\n请选择分支 (0-{len(branches)}): ").strip()
            if choice == "0" or choice == "":
                return None

            choice_num = int(choice)
            if 1 <= choice_num <= len(branches):
                selected = branches[choice_num - 1]
                # 移除 "(当前)" 标记
                if " (当前)" in selected:
                    selected = selected.replace(" (当前)", "")
                return selected
            else:
                print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入数字")

def analyze_recent_changes(days=7, branch=None):
    """分析最近几天的变化"""
    if not check_project_exists():
        return

    # 计算日期范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)

    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')

    print(f"\n🔍 分析HSK项目最近{days}天的变化")
    print(f"📅 时间范围: {start_str} 到 {end_str}")
    if branch:
        print(f"🌿 分析分支: {branch}")
    else:
        print(f"🌿 分析分支: 当前分支")
    print("-" * 50)

    # 创建分析器
    analyzer = GitInterfaceAnalyzer(HSK_PROJECT_PATH)

    # 执行分析
    report = analyzer.analyze_period(start_str, end_str, branch)

    # 保存报告
    branch_suffix = f"_{branch}" if branch else ""
    output_file = f"hsk_analysis_{start_str}_to_{end_str}{branch_suffix}.json"
    analyzer.save_report(report, output_file)

    # 打印结果
    print_summary_report(report)

    return report

def analyze_date_range(start_date, end_date, branch=None):
    """分析指定日期范围的变化"""
    if not check_project_exists():
        return

    print(f"\n🔍 分析HSK项目指定时间段的变化")
    print(f"📅 时间范围: {start_date} 到 {end_date}")
    if branch:
        print(f"🌿 分析分支: {branch}")
    else:
        print(f"🌿 分析分支: 当前分支")
    print("-" * 50)

    # 创建分析器
    analyzer = GitInterfaceAnalyzer(HSK_PROJECT_PATH)

    # 执行分析
    report = analyzer.analyze_period(start_date, end_date, branch)

    # 保存报告
    branch_suffix = f"_{branch}" if branch else ""
    output_file = f"hsk_analysis_{start_date}_to_{end_date}{branch_suffix}.json"
    analyzer.save_report(report, output_file)

    # 打印结果
    print_summary_report(report)

    return report

def print_summary_report(report):
    """打印摘要报告"""
    summary = report['summary']
    
    print("\n" + "="*60)
    print("🎯 HSK项目分析报告")
    print("="*60)
    
    print(f"\n📊 总体统计:")
    print(f"  📡 接口总数: {summary['total_interfaces']}")
    print(f"  🗃️  实体总数: {summary['total_entities']}")
    
    print(f"\n🆕 新增统计:")
    print(f"  ➕ 新增接口: {summary['new_interfaces']}")
    print(f"  ➕ 新增实体: {summary['new_entities']}")
    
    if summary['new_interfaces'] > 0 or summary['new_entities'] > 0:
        print(f"\n✨ 这段时间有新功能开发!")
    
    print(f"\n✏️ 修改统计:")
    print(f"  🔄 修改接口: {summary['modified_interfaces']}")
    print(f"  🔄 修改实体: {summary['modified_entities']}")
    
    print(f"\n🗑️ 删除统计:")
    print(f"  ❌ 删除接口: {summary['deleted_interfaces']}")
    print(f"  ❌ 删除实体: {summary['deleted_entities']}")
    
    # 打印新增接口详情
    if summary['new_interfaces'] > 0:
        print(f"\n🔗 新增接口详情:")
        for interface in report['interfaces']:
            if interface['operation'] == 'ADD':
                print(f"  🌟 {interface['method']} {interface['path']}")
                print(f"     📁 {interface['class_name']}.{interface['method_name']}")
                print(f"     📄 {interface['file_path']}")
                print(f"     🔗 提交: {interface['commit_hash'][:8]} ({interface['commit_date'][:10]})")
                print()
    
    # 打印新增实体详情
    if summary['new_entities'] > 0:
        print(f"\n🗃️ 新增实体详情:")
        for entity in report['entities']:
            if entity['operation'] == 'ADD':
                print(f"  🌟 表: {entity['table_name']} (类: {entity['class_name']})")
                if entity['fields']:
                    print(f"     📋 字段: {', '.join(entity['fields'][:5])}{'...' if len(entity['fields']) > 5 else ''}")
                print(f"     📄 {entity['file_path']}")
                print(f"     🔗 提交: {entity['commit_hash'][:8]} ({entity['commit_date'][:10]})")
                print()

def quick_analysis():
    """快速分析 - 最近7天"""
    return analyze_recent_changes(7)

def weekly_analysis():
    """周分析 - 最近7天"""
    return analyze_recent_changes(7)

def monthly_analysis():
    """月分析 - 最近30天"""
    return analyze_recent_changes(30)

def custom_analysis():
    """自定义时间范围分析"""
    print("\n📅 自定义时间范围分析")
    print("请输入日期范围 (格式: YYYY-MM-DD)")
    
    while True:
        start_date = input("开始日期: ").strip()
        if not start_date:
            print("❌ 开始日期不能为空")
            continue
        
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
            break
        except ValueError:
            print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
    
    while True:
        end_date = input("结束日期: ").strip()
        if not end_date:
            print("❌ 结束日期不能为空")
            continue
        
        try:
            datetime.strptime(end_date, '%Y-%m-%d')
            break
        except ValueError:
            print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
    
    return analyze_date_range(start_date, end_date)

def branch_analysis():
    """指定分支分析"""
    print("\n🌿 指定分支分析")

    # 选择分支
    branch = select_branch()

    print("\n📅 选择时间范围:")
    print("1. 最近7天")
    print("2. 最近30天")
    print("3. 自定义时间范围")

    time_choice = input("\n请选择时间范围 (1-3): ").strip()

    if time_choice == "1":
        return analyze_recent_changes(7, branch)
    elif time_choice == "2":
        return analyze_recent_changes(30, branch)
    elif time_choice == "3":
        print("\n📅 自定义时间范围")
        print("请输入日期范围 (格式: YYYY-MM-DD)")

        while True:
            start_date = input("开始日期: ").strip()
            if not start_date:
                print("❌ 开始日期不能为空")
                continue

            try:
                datetime.strptime(start_date, '%Y-%m-%d')
                break
            except ValueError:
                print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")

        while True:
            end_date = input("结束日期: ").strip()
            if not end_date:
                print("❌ 结束日期不能为空")
                continue

            try:
                datetime.strptime(end_date, '%Y-%m-%d')
                break
            except ValueError:
                print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")

        return analyze_date_range(start_date, end_date, branch)
    else:
        print("❌ 无效选择")
        return None

def main():
    """主函数"""
    print("🚀 HSK项目Git分析工具")
    print("="*50)
    print("专门用于分析HSK Java Spring项目的接口和实体变化")
    
    while True:
        print("\n📋 请选择分析模式:")
        print("1. 🔥 快速分析 (最近7天)")
        print("2. 📅 周分析 (最近7天)")
        print("3. 📆 月分析 (最近30天)")
        print("4. ⚙️  自定义时间范围")
        print("5. 🌿 指定分支分析")
        print("6. 🚪 退出")

        choice = input("\n请输入选择 (1-6): ").strip()

        try:
            if choice == "1":
                quick_analysis()
            elif choice == "2":
                weekly_analysis()
            elif choice == "3":
                monthly_analysis()
            elif choice == "4":
                custom_analysis()
            elif choice == "5":
                branch_analysis()
            elif choice == "6":
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请重新输入")
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 执行过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
