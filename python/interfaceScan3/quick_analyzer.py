#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速分析器 - 专门用于大型项目的快速分析
优化了性能，减少了Git命令调用次数
"""

import os
import re
import json
import subprocess
from datetime import datetime, timedelta
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class SimpleInterface:
    """简化的HTTP接口信息"""
    method: str
    path: str
    class_name: str
    file_path: str
    commit_date: str
    operation: str

@dataclass
class SimpleEntity:
    """简化的数据库实体信息"""
    table_name: str
    class_name: str
    file_path: str
    commit_date: str
    operation: str

class QuickAnalyzer:
    """快速分析器"""
    
    def __init__(self, repo_path: str):
        self.repo_path = Path(repo_path)
        self.interfaces: List[SimpleInterface] = []
        self.entities: List[SimpleEntity] = []
    
    def run_git_command(self, command: List[str]) -> str:
        """执行Git命令"""
        try:
            result = subprocess.run(
                command,
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=30  # 添加超时
            )
            if result.returncode != 0:
                print(f"Git命令执行失败: {' '.join(command)}")
                return ""
            return result.stdout
        except Exception as e:
            print(f"执行Git命令时出错: {e}")
            return ""
    
    def get_recent_commits(self, days: int = 7) -> List[Tuple[str, str]]:
        """获取最近几天的提交"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        command = [
            'git', 'log',
            f'--since={start_str}',
            f'--until={end_str}',
            '--pretty=format:%H|%ci',
            '--reverse'
        ]
        
        output = self.run_git_command(command)
        commits = []
        
        for line in output.strip().split('\n'):
            if line and '|' in line:
                commit_hash, commit_date = line.split('|', 1)
                commits.append((commit_hash.strip(), commit_date.strip()))
        
        return commits
    
    def analyze_commit_diff(self, commit_hash: str, commit_date: str):
        """分析单个提交的差异"""
        # 获取提交的文件变更
        command = ['git', 'show', '--name-status', '--format=', commit_hash]
        output = self.run_git_command(command)
        
        java_files = []
        for line in output.strip().split('\n'):
            if line and '\t' in line:
                parts = line.split('\t')
                if len(parts) >= 2:
                    status = parts[0]
                    file_path = parts[1]
                    if file_path.endswith('.java'):
                        java_files.append((status, file_path))
        
        # 分析每个Java文件
        for status, file_path in java_files:
            operation = "MODIFY"
            if status == "A":
                operation = "ADD"
            elif status == "D":
                operation = "DELETE"
            
            if operation != "DELETE":
                # 获取文件内容
                content_command = ['git', 'show', f'{commit_hash}:{file_path}']
                content = self.run_git_command(content_command)
                
                if content:
                    # 快速检查是否是Controller
                    if self.is_controller_file(content):
                        interfaces = self.extract_interfaces_quick(content, file_path, commit_date, operation)
                        self.interfaces.extend(interfaces)
                    
                    # 快速检查是否是Entity
                    if self.is_entity_file(content):
                        entities = self.extract_entities_quick(content, file_path, commit_date, operation)
                        self.entities.extend(entities)
    
    def is_controller_file(self, content: str) -> bool:
        """快速检查是否是Controller文件"""
        controller_indicators = [
            '@RestController',
            '@Controller',
            '@RequestMapping'
        ]
        return any(indicator in content for indicator in controller_indicators)
    
    def is_entity_file(self, content: str) -> bool:
        """快速检查是否是Entity文件"""
        entity_indicators = [
            '@Entity',
            '@Table',
            '@TableName'
        ]
        return any(indicator in content for indicator in entity_indicators)
    
    def extract_interfaces_quick(self, content: str, file_path: str, 
                                commit_date: str, operation: str) -> List[SimpleInterface]:
        """快速提取接口信息"""
        interfaces = []
        lines = content.split('\n')
        
        class_name = ""
        base_path = ""
        
        # 提取类名和基础路径
        for line in lines:
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                class_name = class_match.group(1)
            
            if '@RequestMapping' in line:
                path_match = re.search(r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']', line)
                if path_match:
                    base_path = path_match.group(1)
        
        # 查找HTTP方法注解
        http_patterns = {
            'GET': r'@GetMapping',
            'POST': r'@PostMapping',
            'PUT': r'@PutMapping',
            'DELETE': r'@DeleteMapping',
            'PATCH': r'@PatchMapping'
        }
        
        for i, line in enumerate(lines):
            for method, pattern in http_patterns.items():
                if re.search(pattern, line):
                    # 提取路径
                    path = ""
                    path_match = re.search(r'["\']([^"\']+)["\']', line)
                    if path_match:
                        path = path_match.group(1)
                    
                    # 构建完整路径
                    full_path = base_path + path if base_path else path
                    
                    if class_name and full_path:
                        interface = SimpleInterface(
                            method=method,
                            path=full_path,
                            class_name=class_name,
                            file_path=file_path,
                            commit_date=commit_date[:10],  # 只保留日期部分
                            operation=operation
                        )
                        interfaces.append(interface)
        
        return interfaces
    
    def extract_entities_quick(self, content: str, file_path: str,
                              commit_date: str, operation: str) -> List[SimpleEntity]:
        """快速提取实体信息"""
        entities = []
        lines = content.split('\n')
        
        class_name = ""
        table_name = ""
        
        for line in lines:
            # 提取类名
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                class_name = class_match.group(1)
                if not table_name:
                    # 默认表名为类名转下划线
                    table_name = self.camel_to_snake(class_name)
            
            # 提取表名
            if '@TableName' in line or '@Table' in line:
                name_match = re.search(r'["\']([^"\']+)["\']', line)
                if name_match:
                    table_name = name_match.group(1)
        
        if class_name:
            entity = SimpleEntity(
                table_name=table_name,
                class_name=class_name,
                file_path=file_path,
                commit_date=commit_date[:10],  # 只保留日期部分
                operation=operation
            )
            entities.append(entity)
        
        return entities
    
    def camel_to_snake(self, name: str) -> str:
        """驼峰命名转下划线命名"""
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    def analyze_recent_days(self, days: int = 7) -> Dict:
        """分析最近几天的变化"""
        print(f"🔍 快速分析最近{days}天的变化...")
        
        commits = self.get_recent_commits(days)
        print(f"📊 找到 {len(commits)} 个提交")
        
        if len(commits) > 50:
            print(f"⚠️  提交数量较多，将分析最近的50个提交")
            commits = commits[-50:]  # 只分析最近的50个提交
        
        for i, (commit_hash, commit_date) in enumerate(commits):
            print(f"📝 分析提交 {i+1}/{len(commits)}: {commit_hash[:8]} ({commit_date[:10]})")
            self.analyze_commit_diff(commit_hash, commit_date)
        
        return self.generate_report()
    
    def generate_report(self) -> Dict:
        """生成分析报告"""
        report = {
            "summary": {
                "total_interfaces": len(self.interfaces),
                "total_entities": len(self.entities),
                "new_interfaces": len([i for i in self.interfaces if i.operation == "ADD"]),
                "new_entities": len([e for e in self.entities if e.operation == "ADD"]),
                "modified_interfaces": len([i for i in self.interfaces if i.operation == "MODIFY"]),
                "modified_entities": len([e for e in self.entities if e.operation == "MODIFY"])
            },
            "interfaces": [asdict(interface) for interface in self.interfaces],
            "entities": [asdict(entity) for entity in self.entities]
        }
        
        return report

def main():
    """主函数"""
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    
    print("🚀 HSK项目快速分析工具")
    print("="*50)
    
    if not os.path.exists(HSK_PROJECT_PATH):
        print(f"❌ 项目路径不存在: {HSK_PROJECT_PATH}")
        return
    
    analyzer = QuickAnalyzer(HSK_PROJECT_PATH)
    
    while True:
        print("\n📋 选择分析时间范围:")
        print("1. 最近3天")
        print("2. 最近7天")
        print("3. 最近15天")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        try:
            if choice == "1":
                report = analyzer.analyze_recent_days(3)
            elif choice == "2":
                report = analyzer.analyze_recent_days(7)
            elif choice == "3":
                report = analyzer.analyze_recent_days(15)
            elif choice == "4":
                print("👋 退出程序")
                break
            else:
                print("❌ 无效选择")
                continue
            
            # 打印结果
            summary = report['summary']
            print(f"\n🎯 分析结果:")
            print(f"  📡 接口总数: {summary['total_interfaces']}")
            print(f"  🗃️  实体总数: {summary['total_entities']}")
            print(f"  ➕ 新增接口: {summary['new_interfaces']}")
            print(f"  ➕ 新增实体: {summary['new_entities']}")
            
            # 保存报告
            output_file = f"quick_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"📄 详细报告已保存到: {output_file}")
            
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 执行过程中出现错误: {e}")

if __name__ == "__main__":
    main()
