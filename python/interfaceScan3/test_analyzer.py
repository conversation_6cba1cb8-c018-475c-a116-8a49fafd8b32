#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析器 - 验证基本功能
"""

import os
import subprocess
from datetime import datetime, timedelta

def test_git_access():
    """测试Git访问"""
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    
    print("🔍 测试Git访问...")
    print(f"项目路径: {HSK_PROJECT_PATH}")
    
    # 检查路径是否存在
    if not os.path.exists(HSK_PROJECT_PATH):
        print("❌ 项目路径不存在")
        return False
    
    # 检查是否是Git仓库
    git_path = os.path.join(HSK_PROJECT_PATH, '.git')
    if not os.path.exists(git_path):
        print("❌ 不是Git仓库")
        return False
    
    print("✅ 路径验证成功")
    
    # 测试Git命令
    try:
        # 获取最近的提交
        result = subprocess.run(
            ['git', 'log', '--oneline', '-5'],
            cwd=HSK_PROJECT_PATH,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Git命令执行成功")
            print("最近5个提交:")
            for line in result.stdout.strip().split('\n')[:5]:
                if line:
                    print(f"  {line}")
            return True
        else:
            print(f"❌ Git命令执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Git命令执行异常: {e}")
        return False

def test_recent_commits():
    """测试获取最近提交"""
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    
    print("\n🔍 测试获取最近3天的提交...")
    
    # 计算日期范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    print(f"时间范围: {start_str} 到 {end_str}")
    
    try:
        command = [
            'git', 'log',
            f'--since={start_str}',
            f'--until={end_str}',
            '--pretty=format:%H|%ci|%s',
            '--reverse'
        ]
        
        result = subprocess.run(
            command,
            cwd=HSK_PROJECT_PATH,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30
        )
        
        if result.returncode == 0:
            commits = []
            for line in result.stdout.strip().split('\n'):
                if line and '|' in line:
                    parts = line.split('|', 2)
                    if len(parts) >= 3:
                        commit_hash, commit_date, message = parts
                        commits.append((commit_hash.strip(), commit_date.strip(), message.strip()))
            
            print(f"✅ 找到 {len(commits)} 个提交")
            
            # 显示前5个提交
            for i, (hash_val, date, msg) in enumerate(commits[:5]):
                print(f"  {i+1}. {hash_val[:8]} ({date[:10]}) {msg[:50]}...")
            
            if len(commits) > 5:
                print(f"  ... 还有 {len(commits) - 5} 个提交")
            
            return commits
        else:
            print(f"❌ 获取提交失败: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"❌ 获取提交异常: {e}")
        return []

def test_file_changes():
    """测试获取文件变更"""
    HSK_PROJECT_PATH = r"D:\code\Hsk-java"
    
    print("\n🔍 测试获取最新提交的文件变更...")
    
    try:
        # 获取最新提交的哈希
        result = subprocess.run(
            ['git', 'log', '-1', '--pretty=format:%H'],
            cwd=HSK_PROJECT_PATH,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=10
        )
        
        if result.returncode != 0:
            print("❌ 获取最新提交失败")
            return
        
        latest_commit = result.stdout.strip()
        print(f"最新提交: {latest_commit[:8]}")
        
        # 获取文件变更
        result = subprocess.run(
            ['git', 'show', '--name-status', '--format=', latest_commit],
            cwd=HSK_PROJECT_PATH,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=10
        )
        
        if result.returncode == 0:
            java_files = []
            for line in result.stdout.strip().split('\n'):
                if line and '\t' in line:
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        status = parts[0]
                        file_path = parts[1]
                        if file_path.endswith('.java'):
                            java_files.append((status, file_path))
            
            print(f"✅ 找到 {len(java_files)} 个Java文件变更")
            
            # 显示前5个文件
            for i, (status, path) in enumerate(java_files[:5]):
                print(f"  {i+1}. {status} {path}")
            
            if len(java_files) > 5:
                print(f"  ... 还有 {len(java_files) - 5} 个文件")
                
        else:
            print(f"❌ 获取文件变更失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 获取文件变更异常: {e}")

def main():
    """主函数"""
    print("🚀 HSK项目分析器测试")
    print("="*50)
    
    # 测试Git访问
    if not test_git_access():
        print("\n❌ Git访问测试失败，请检查项目路径和Git配置")
        return
    
    # 测试获取最近提交
    commits = test_recent_commits()
    
    # 测试文件变更
    test_file_changes()
    
    print("\n🎯 测试总结:")
    if commits:
        print(f"✅ 基本功能正常，最近3天有 {len(commits)} 个提交")
        print("✅ 可以继续使用完整的分析器")
    else:
        print("⚠️  最近3天没有提交，可以尝试更长的时间范围")
    
    print("\n📋 使用建议:")
    print("1. 运行 python quick_analyzer.py 进行快速分析")
    print("2. 运行 python hsk_project_analyzer.py 进行详细分析")
    print("3. 双击 run_hsk_analyzer.bat 快速启动")

if __name__ == "__main__":
    main()
